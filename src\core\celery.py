"""
Celery异步任务队列配置
Celery Async Task Queue Configuration
"""

from celery import Celery
from src.core.config import settings

# 创建Celery应用实例
celery_app = Celery(
    "mining_road_design",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "src.tasks.road_design",
        "src.tasks.gis_processing", 
        "src.tasks.optimization",
        "src.tasks.conflict_detection"
    ]
)

# Celery配置
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30分钟超时
    task_soft_time_limit=25 * 60,  # 25分钟软超时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# 任务路由配置
celery_app.conf.task_routes = {
    "src.tasks.road_design.*": {"queue": "road_design"},
    "src.tasks.gis_processing.*": {"queue": "gis_processing"},
    "src.tasks.optimization.*": {"queue": "optimization"},
    "src.tasks.conflict_detection.*": {"queue": "conflict_detection"},
}

# 定期任务配置（如果需要）
celery_app.conf.beat_schedule = {
    # 'cleanup-temp-files': {
    #     'task': 'src.tasks.maintenance.cleanup_temp_files',
    #     'schedule': 3600.0,  # 每小时执行一次
    # },
}


@celery_app.task(bind=True)
def debug_task(self):
    """调试任务"""
    print(f'Request: {self.request!r}')
    return "Celery is working!"


if __name__ == "__main__":
    celery_app.start()