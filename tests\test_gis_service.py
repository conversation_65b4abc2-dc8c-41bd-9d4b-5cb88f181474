"""
GIS数据处理服务测试
GIS Data Processing Service Tests
"""

import sys
import os
import tempfile
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.gis_service import (
    TerrainDataImporter, TerrainDataProcessor, TerrainPoint, ImportResult,
    create_terrain_importer, create_terrain_processor, import_terrain_file,
    get_supported_terrain_formats, create_sample_xyz_file, create_sample_csv_file
)


def test_terrain_data_importer():
    """测试地形数据导入器"""
    print("🔍 测试地形数据导入器...")
    
    importer = create_terrain_importer()
    
    # 测试支持的格式
    formats = importer.get_supported_formats()
    expected_formats = ['.xyz', '.csv', '.txt', '.dem', '.asc', '.json']
    
    for fmt in expected_formats:
        assert fmt in formats, f"缺少支持的格式: {fmt}"
    
    print(f"✅ 支持的格式: {', '.join(formats)}")


def test_xyz_file_import():
    """测试XYZ文件导入"""
    print("\n🔍 测试XYZ文件导入...")
    
    # 创建临时XYZ文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.xyz', delete=False, encoding='utf-8') as f:
        f.write("# Sample XYZ data\n")
        f.write("0.0 0.0 100.0\n")
        f.write("10.0 0.0 101.0\n")
        f.write("0.0 10.0 102.0\n")
        f.write("10.0 10.0 103.0\n")
        temp_file = f.name
    
    try:
        # 导入文件
        result = import_terrain_file(temp_file)
        
        assert result.success, f"导入失败: {result.message}"
        assert result.point_count == 4, f"点数量错误: {result.point_count}"
        assert result.bounds is not None, "未计算边界"
        
        print(f"✅ XYZ文件导入成功: {result.point_count} 个点")
        print(f"   边界: ({result.bounds.min_x}, {result.bounds.min_y}) - ({result.bounds.max_x}, {result.bounds.max_y})")
        print(f"   高程范围: {result.bounds.min_z} - {result.bounds.max_z}")
        
    finally:
        os.unlink(temp_file)


def test_csv_file_import():
    """测试CSV文件导入"""
    print("\n🔍 测试CSV文件导入...")
    
    # 创建临时CSV文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        f.write("X,Y,Z,Type\n")
        f.write("0.0,0.0,100.0,ground\n")
        f.write("10.0,0.0,101.0,ground\n")
        f.write("0.0,10.0,102.0,vegetation\n")
        f.write("10.0,10.0,103.0,building\n")
        temp_file = f.name
    
    try:
        # 导入文件
        result = import_terrain_file(temp_file)
        
        assert result.success, f"导入失败: {result.message}"
        assert result.point_count == 4, f"点数量错误: {result.point_count}"
        
        print(f"✅ CSV文件导入成功: {result.point_count} 个点")
        
    finally:
        os.unlink(temp_file)


def test_ascii_grid_import():
    """测试ASCII Grid文件导入"""
    print("\n🔍 测试ASCII Grid文件导入...")
    
    # 创建临时ASCII Grid文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.asc', delete=False, encoding='utf-8') as f:
        f.write("ncols 3\n")
        f.write("nrows 3\n")
        f.write("xllcorner 0.0\n")
        f.write("yllcorner 0.0\n")
        f.write("cellsize 10.0\n")
        f.write("nodata_value -9999\n")
        f.write("100.0 101.0 102.0\n")
        f.write("103.0 104.0 105.0\n")
        f.write("106.0 107.0 108.0\n")
        temp_file = f.name
    
    try:
        # 导入文件
        result = import_terrain_file(temp_file)
        
        assert result.success, f"导入失败: {result.message}"
        assert result.point_count == 9, f"点数量错误: {result.point_count}"
        
        print(f"✅ ASCII Grid文件导入成功: {result.point_count} 个点")
        
    finally:
        os.unlink(temp_file)


def test_geojson_import():
    """测试GeoJSON文件导入"""
    print("\n🔍 测试GeoJSON文件导入...")
    
    # 创建临时GeoJSON文件
    geojson_data = {
        "type": "FeatureCollection",
        "features": [
            {
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": [0.0, 0.0, 100.0]
                },
                "properties": {
                    "name": "Point 1",
                    "type": "ground"
                }
            },
            {
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": [10.0, 10.0, 105.0]
                },
                "properties": {
                    "name": "Point 2",
                    "type": "building"
                }
            }
        ]
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(geojson_data, f)
        temp_file = f.name
    
    try:
        # 导入文件
        result = import_terrain_file(temp_file)
        
        assert result.success, f"导入失败: {result.message}"
        assert result.point_count == 2, f"点数量错误: {result.point_count}"
        
        print(f"✅ GeoJSON文件导入成功: {result.point_count} 个点")
        
    finally:
        os.unlink(temp_file)


def test_file_validation():
    """测试文件验证"""
    print("\n🔍 测试文件验证...")
    
    importer = create_terrain_importer()
    
    # 测试不存在的文件
    valid, message = importer.validate_file_format("nonexistent.xyz")
    assert not valid, "不存在的文件应该验证失败"
    print(f"✅ 不存在文件验证: {message}")
    
    # 测试不支持的格式
    with tempfile.NamedTemporaryFile(suffix='.unknown', delete=False) as f:
        temp_file = f.name
    
    try:
        valid, message = importer.validate_file_format(temp_file)
        assert not valid, "不支持的格式应该验证失败"
        print(f"✅ 不支持格式验证: {message}")
        
    finally:
        os.unlink(temp_file)


def test_terrain_processor():
    """测试地形数据处理器"""
    print("\n🔍 测试地形数据处理器...")
    
    processor = create_terrain_processor()
    
    # 创建示例文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.xyz', delete=False, encoding='utf-8') as f:
        for i in range(5):
            for j in range(5):
                x = i * 2.0
                y = j * 2.0
                z = 100.0 + i + j
                f.write(f"{x} {y} {z}\n")
        temp_file = f.name
    
    try:
        # 处理文件
        result = processor.process_terrain_file(temp_file, output_format='json')
        
        assert result['success'], f"处理失败: {result.get('message')}"
        assert result['point_count'] == 25, f"点数量错误: {result['point_count']}"
        
        print(f"✅ 地形数据处理成功: {result['point_count']} 个点")
        
        # 测试简化处理
        result_simplified = processor.process_terrain_file(
            temp_file, output_format='json', simplify_tolerance=3.0
        )
        
        assert result_simplified['success'], "简化处理失败"
        print(f"✅ 简化处理成功: {result_simplified['point_count']} 个点")
        
    finally:
        os.unlink(temp_file)


def test_terrain_model_creation():
    """测试地形模型创建"""
    print("\n🔍 测试地形模型创建...")
    
    processor = create_terrain_processor()
    
    # 创建示例文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.xyz', delete=False, encoding='utf-8') as f:
        for i in range(3):
            for j in range(3):
                x = i * 10.0
                y = j * 10.0
                z = 100.0 + i * 2 + j
                f.write(f"{x} {y} {z}\n")
        temp_file = f.name
    
    try:
        # 创建地形模型
        terrain_model = processor.create_terrain_model(
            temp_file, 
            name="测试地形",
            description="用于测试的示例地形数据"
        )
        
        assert terrain_model is not None, "地形模型创建失败"
        assert terrain_model['name'] == "测试地形", "地形模型名称错误"
        assert terrain_model['point_count'] == 9, "地形模型点数量错误"
        
        print(f"✅ 地形模型创建成功: {terrain_model['name']}")
        print(f"   点数量: {terrain_model['point_count']}")
        print(f"   分辨率: {terrain_model['resolution']:.2f}m")
        
    finally:
        os.unlink(temp_file)


def test_sample_file_creation():
    """测试示例文件创建"""
    print("\n🔍 测试示例文件创建...")
    
    # 创建示例XYZ文件
    with tempfile.NamedTemporaryFile(suffix='.xyz', delete=False) as f:
        xyz_file = f.name
    
    try:
        create_sample_xyz_file(xyz_file, grid_size=5)
        
        # 验证文件内容
        result = import_terrain_file(xyz_file)
        assert result.success, "示例XYZ文件导入失败"
        assert result.point_count == 25, f"示例XYZ文件点数量错误: {result.point_count}"
        
        print(f"✅ 示例XYZ文件创建成功: {result.point_count} 个点")
        
    finally:
        os.unlink(xyz_file)
    
    # 创建示例CSV文件
    with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as f:
        csv_file = f.name
    
    try:
        create_sample_csv_file(csv_file, grid_size=4)
        
        # 验证文件内容
        result = import_terrain_file(csv_file)
        assert result.success, "示例CSV文件导入失败"
        assert result.point_count == 16, f"示例CSV文件点数量错误: {result.point_count}"
        
        print(f"✅ 示例CSV文件创建成功: {result.point_count} 个点")
        
    finally:
        os.unlink(csv_file)


def test_error_handling():
    """测试错误处理"""
    print("\n🔍 测试错误处理...")
    
    # 测试格式错误的文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.xyz', delete=False, encoding='utf-8') as f:
        f.write("invalid data\n")
        f.write("not numbers here\n")
        f.write("1.0 2.0 3.0\n")  # 只有一行有效数据
        temp_file = f.name
    
    try:
        result = import_terrain_file(temp_file)
        
        assert result.success, "应该成功导入有效数据"
        assert result.point_count == 1, "应该只有1个有效点"
        assert len(result.errors) == 2, "应该有2个错误"
        
        print(f"✅ 错误处理测试通过: {result.point_count} 个有效点, {len(result.errors)} 个错误")
        
    finally:
        os.unlink(temp_file)


def run_all_tests():
    """运行所有测试"""
    print("=" * 70)
    print("GIS数据处理服务测试")
    print("GIS Data Processing Service Tests")
    print("=" * 70)
    
    try:
        test_terrain_data_importer()
        test_xyz_file_import()
        test_csv_file_import()
        test_ascii_grid_import()
        test_geojson_import()
        test_file_validation()
        test_terrain_processor()
        test_terrain_model_creation()
        test_sample_file_creation()
        test_error_handling()
        
        print("\n" + "=" * 70)
        print("🎉 所有GIS数据处理测试通过！")
        print("✅ GIS数据处理服务功能正常")
        print("=" * 70)
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        print("=" * 70)
        return False


if __name__ == "__main__":
    run_all_tests()