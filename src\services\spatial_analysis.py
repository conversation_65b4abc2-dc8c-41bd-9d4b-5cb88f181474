"""
空间分析工具
Spatial Analysis Tools
"""

import math
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum

from src.services.gis_service import TerrainPoint, BoundingBox3D
from src.services.surface_modeling import Surface3D, Triangle


class AnalysisType(str, Enum):
    """分析类型"""
    BUFFER = "buffer"
    OVERLAY = "overlay"
    SLOPE = "slope"
    ASPECT = "aspect"
    WATERSHED = "watershed"
    VISIBILITY = "visibility"
    DISTANCE = "distance"
    CONTOUR = "contour"


@dataclass
class AnalysisResult:
    """分析结果"""
    analysis_type: AnalysisType
    success: bool
    message: str
    data: Dict[str, Any]
    execution_time: float = 0.0
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}


@dataclass
class BufferZone:
    """缓冲区"""
    center_points: List[TerrainPoint]
    radius: float
    boundary_points: List[TerrainPoint]
    area: float = 0.0


@dataclass
class SlopeAnalysisResult:
    """坡度分析结果"""
    grid_points: List[TerrainPoint]
    slope_values: List[float]  # 对应每个网格点的坡度值
    aspect_values: List[float]  # 对应每个网格点的坡向值
    statistics: Dict[str, float]


class SpatialAnalyzer:
    """空间分析器"""
    
    def __init__(self):
        self.analysis_methods = {
            AnalysisType.BUFFER: self._buffer_analysis,
            AnalysisType.SLOPE: self._slope_analysis,
            AnalysisType.ASPECT: self._aspect_analysis,
            AnalysisType.DISTANCE: self._distance_analysis,
            AnalysisType.VISIBILITY: self._visibility_analysis,
            AnalysisType.CONTOUR: self._contour_analysis
        }
    
    def analyze(self, analysis_type: AnalysisType, data: Dict[str, Any]) -> AnalysisResult:
        """执行空间分析"""
        import time
        start_time = time.time()
        
        try:
            if analysis_type not in self.analysis_methods:
                return AnalysisResult(
                    analysis_type=analysis_type,
                    success=False,
                    message=f"不支持的分析类型: {analysis_type}",
                    data={}
                )
            
            # 执行分析
            analysis_func = self.analysis_methods[analysis_type]
            result_data = analysis_func(data)
            
            execution_time = time.time() - start_time
            
            return AnalysisResult(
                analysis_type=analysis_type,
                success=True,
                message="分析完成",
                data=result_data,
                execution_time=execution_time,
                parameters=data
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return AnalysisResult(
                analysis_type=analysis_type,
                success=False,
                message=f"分析失败: {str(e)}",
                data={},
                execution_time=execution_time,
                parameters=data
            )
    
    def _buffer_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """缓冲区分析"""
        points = data.get('points', [])
        radius = data.get('radius', 10.0)
        resolution = data.get('resolution', 1.0)
        
        if not points:
            raise ValueError("缺少输入点数据")
        
        buffer_zones = []
        
        for point in points:
            # 生成缓冲区边界点
            boundary_points = self._generate_circle_points(point, radius, resolution)
            
            # 计算缓冲区面积
            area = math.pi * radius * radius
            
            buffer_zone = BufferZone(
                center_points=[point],
                radius=radius,
                boundary_points=boundary_points,
                area=area
            )
            buffer_zones.append(buffer_zone)
        
        return {
            'buffer_zones': buffer_zones,
            'total_area': sum(zone.area for zone in buffer_zones),
            'zone_count': len(buffer_zones)
        }
    
    def _slope_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """坡度分析"""
        surface = data.get('surface')
        grid_size = data.get('grid_size', 10.0)
        
        if not surface:
            raise ValueError("缺少地表数据")
        
        # 生成分析网格
        grid_points = self._generate_analysis_grid(surface.bounds, grid_size)
        
        slope_values = []
        aspect_values = []
        
        for point in grid_points:
            slope, aspect = self._calculate_slope_aspect(surface, point.x, point.y)
            slope_values.append(slope)
            aspect_values.append(aspect)
        
        # 计算统计信息
        statistics = {
            'min_slope': min(slope_values) if slope_values else 0,
            'max_slope': max(slope_values) if slope_values else 0,
            'avg_slope': sum(slope_values) / len(slope_values) if slope_values else 0,
            'min_aspect': min(aspect_values) if aspect_values else 0,
            'max_aspect': max(aspect_values) if aspect_values else 0,
            'avg_aspect': sum(aspect_values) / len(aspect_values) if aspect_values else 0
        }
        
        result = SlopeAnalysisResult(
            grid_points=grid_points,
            slope_values=slope_values,
            aspect_values=aspect_values,
            statistics=statistics
        )
        
        return {
            'slope_analysis': result,
            'grid_count': len(grid_points),
            'statistics': statistics
        }
    
    def _aspect_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """坡向分析"""
        # 坡向分析与坡度分析类似，这里复用坡度分析的结果
        return self._slope_analysis(data)
    
    def _distance_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """距离分析"""
        source_points = data.get('source_points', [])
        target_points = data.get('target_points', [])
        analysis_type = data.get('distance_type', 'euclidean')  # euclidean, manhattan
        
        if not source_points or not target_points:
            raise ValueError("缺少源点或目标点数据")
        
        distance_matrix = []
        
        for i, source in enumerate(source_points):
            distances = []
            for j, target in enumerate(target_points):
                if analysis_type == 'euclidean':
                    distance = self._euclidean_distance(source, target)
                elif analysis_type == 'manhattan':
                    distance = self._manhattan_distance(source, target)
                else:
                    distance = self._euclidean_distance(source, target)
                
                distances.append({
                    'source_index': i,
                    'target_index': j,
                    'distance': distance
                })
            distance_matrix.append(distances)
        
        # 找到最近和最远的距离
        all_distances = [d['distance'] for row in distance_matrix for d in row]
        
        return {
            'distance_matrix': distance_matrix,
            'min_distance': min(all_distances) if all_distances else 0,
            'max_distance': max(all_distances) if all_distances else 0,
            'avg_distance': sum(all_distances) / len(all_distances) if all_distances else 0,
            'analysis_type': analysis_type
        }
    
    def _visibility_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """可视性分析"""
        surface = data.get('surface')
        observer_point = data.get('observer_point')
        observer_height = data.get('observer_height', 1.7)
        target_height = data.get('target_height', 0.0)
        max_distance = data.get('max_distance', 1000.0)
        
        if not surface or not observer_point:
            raise ValueError("缺少地表数据或观察点")
        
        # 生成分析网格
        grid_size = max_distance / 50  # 50个采样点
        grid_points = self._generate_analysis_grid(surface.bounds, grid_size)
        
        visible_points = []
        invisible_points = []
        
        for point in grid_points:
            # 检查距离限制
            distance = self._euclidean_distance(observer_point, point)
            if distance > max_distance:
                continue
            
            # 检查可视性
            if self._is_visible(surface, observer_point, point, observer_height, target_height):
                visible_points.append(point)
            else:
                invisible_points.append(point)
        
        return {
            'observer_point': observer_point,
            'visible_points': visible_points,
            'invisible_points': invisible_points,
            'visible_count': len(visible_points),
            'invisible_count': len(invisible_points),
            'visibility_ratio': len(visible_points) / (len(visible_points) + len(invisible_points)) if (len(visible_points) + len(invisible_points)) > 0 else 0
        }
    
    def _contour_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """等高线分析"""
        surface = data.get('surface')
        interval = data.get('interval', 5.0)
        min_elevation = data.get('min_elevation')
        max_elevation = data.get('max_elevation')
        
        if not surface:
            raise ValueError("缺少地表数据")
        
        # 确定高程范围
        if min_elevation is None:
            min_elevation = surface.bounds.min_z
        if max_elevation is None:
            max_elevation = surface.bounds.max_z
        
        # 生成等高线
        contour_lines = []
        elevation = min_elevation
        
        while elevation <= max_elevation:
            lines = self._extract_contour_lines(surface, elevation)
            if lines:
                contour_lines.append({
                    'elevation': elevation,
                    'lines': lines,
                    'line_count': len(lines)
                })
            elevation += interval
        
        return {
            'contour_lines': contour_lines,
            'elevation_range': (min_elevation, max_elevation),
            'interval': interval,
            'total_lines': sum(c['line_count'] for c in contour_lines)
        }
    
    def _generate_circle_points(self, center: TerrainPoint, radius: float, 
                              resolution: float) -> List[TerrainPoint]:
        """生成圆形边界点"""
        points = []
        circumference = 2 * math.pi * radius
        num_points = max(8, int(circumference / resolution))
        
        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            x = center.x + radius * math.cos(angle)
            y = center.y + radius * math.sin(angle)
            z = center.z  # 保持相同高程
            points.append(TerrainPoint(x, y, z))
        
        return points
    
    def _generate_analysis_grid(self, bounds: BoundingBox3D, grid_size: float) -> List[TerrainPoint]:
        """生成分析网格"""
        grid_points = []
        
        x = bounds.min_x
        while x <= bounds.max_x:
            y = bounds.min_y
            while y <= bounds.max_y:
                grid_points.append(TerrainPoint(x, y, 0.0))
                y += grid_size
            x += grid_size
        
        return grid_points
    
    def _calculate_slope_aspect(self, surface: Surface3D, x: float, y: float,
                              sample_distance: float = 1.0) -> Tuple[float, float]:
        """计算坡度和坡向"""
        # 在指定点周围采样
        points = [
            (x + sample_distance, y),
            (x - sample_distance, y),
            (x, y + sample_distance),
            (x, y - sample_distance)
        ]
        
        elevations = []
        for px, py in points:
            elevation = self._interpolate_elevation(surface, px, py)
            elevations.append(elevation)
        
        # 计算梯度
        dx = (elevations[0] - elevations[1]) / (2 * sample_distance)
        dy = (elevations[2] - elevations[3]) / (2 * sample_distance)
        
        # 计算坡度（度）
        slope = math.degrees(math.atan(math.sqrt(dx*dx + dy*dy)))
        
        # 计算坡向（度，北为0度）
        aspect = math.degrees(math.atan2(dy, dx))
        if aspect < 0:
            aspect += 360
        
        return slope, aspect
    
    def _interpolate_elevation(self, surface: Surface3D, x: float, y: float) -> float:
        """在地表上插值高程"""
        # 查找包含该点的三角形
        for triangle in surface.triangles:
            if triangle.contains_point(x, y):
                return triangle.interpolate_elevation(x, y)
        
        # 如果没有找到包含的三角形，使用最近的三角形
        if surface.triangles:
            nearest_triangle = min(surface.triangles,
                                 key=lambda t: min(
                                     math.sqrt((t.p1.x - x)**2 + (t.p1.y - y)**2),
                                     math.sqrt((t.p2.x - x)**2 + (t.p2.y - y)**2),
                                     math.sqrt((t.p3.x - x)**2 + (t.p3.y - y)**2)
                                 ))
            return nearest_triangle.interpolate_elevation(x, y)
        
        return 0.0
    
    def _euclidean_distance(self, p1: TerrainPoint, p2: TerrainPoint) -> float:
        """计算欧几里得距离"""
        return math.sqrt((p1.x - p2.x)**2 + (p1.y - p2.y)**2 + (p1.z - p2.z)**2)
    
    def _manhattan_distance(self, p1: TerrainPoint, p2: TerrainPoint) -> float:
        """计算曼哈顿距离"""
        return abs(p1.x - p2.x) + abs(p1.y - p2.y) + abs(p1.z - p2.z)
    
    def _is_visible(self, surface: Surface3D, observer: TerrainPoint, target: TerrainPoint,
                   observer_height: float, target_height: float) -> bool:
        """检查两点间的可视性"""
        # 计算视线方向
        dx = target.x - observer.x
        dy = target.y - observer.y
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance == 0:
            return True
        
        # 沿视线采样检查遮挡
        num_samples = max(10, int(distance / 5.0))  # 每5米一个采样点
        
        observer_elevation = self._interpolate_elevation(surface, observer.x, observer.y) + observer_height
        target_elevation = self._interpolate_elevation(surface, target.x, target.y) + target_height
        
        for i in range(1, num_samples):
            t = i / num_samples
            sample_x = observer.x + t * dx
            sample_y = observer.y + t * dy
            
            # 计算视线在该点的高程
            line_elevation = observer_elevation + t * (target_elevation - observer_elevation)
            
            # 计算地面高程
            ground_elevation = self._interpolate_elevation(surface, sample_x, sample_y)
            
            # 如果地面高程高于视线，则被遮挡
            if ground_elevation > line_elevation:
                return False
        
        return True
    
    def _extract_contour_lines(self, surface: Surface3D, elevation: float) -> List[List[Tuple[float, float]]]:
        """提取等高线"""
        contour_lines = []
        
        for triangle in surface.triangles:
            # 检查三角形是否与等高线相交
            intersections = []
            
            edges = [
                (triangle.p1, triangle.p2),
                (triangle.p2, triangle.p3),
                (triangle.p3, triangle.p1)
            ]
            
            for p1, p2 in edges:
                if ((p1.z <= elevation <= p2.z) or (p2.z <= elevation <= p1.z)) and p1.z != p2.z:
                    # 线性插值找到交点
                    t = (elevation - p1.z) / (p2.z - p1.z)
                    x = p1.x + t * (p2.x - p1.x)
                    y = p1.y + t * (p2.y - p1.y)
                    intersections.append((x, y))
            
            if len(intersections) >= 2:
                contour_lines.append(intersections)
        
        return contour_lines


class TerrainAnalyzer:
    """地形分析器"""
    
    def __init__(self):
        self.spatial_analyzer = SpatialAnalyzer()
    
    def analyze_terrain_slope(self, surface: Surface3D, grid_size: float = 10.0) -> AnalysisResult:
        """分析地形坡度"""
        data = {
            'surface': surface,
            'grid_size': grid_size
        }
        return self.spatial_analyzer.analyze(AnalysisType.SLOPE, data)
    
    def analyze_drainage_basin(self, surface: Surface3D, pour_point: TerrainPoint) -> Dict[str, Any]:
        """分析流域（简化实现）"""
        # 这里实现一个简化的流域分析
        # 实际应用中需要更复杂的水文分析算法
        
        basin_points = []
        flow_directions = []
        
        # 生成分析网格
        grid_points = self.spatial_analyzer._generate_analysis_grid(surface.bounds, 10.0)
        
        for point in grid_points:
            # 计算流向（简化：指向最陡下降方向）
            flow_direction = self._calculate_flow_direction(surface, point)
            flow_directions.append(flow_direction)
            
            # 简化的流域判断：如果流向指向汇流点，则属于该流域
            if self._flows_to_point(point, pour_point, flow_direction):
                basin_points.append(point)
        
        return {
            'basin_points': basin_points,
            'pour_point': pour_point,
            'basin_area': len(basin_points) * 100,  # 假设每个网格点代表100平方米
            'flow_directions': flow_directions
        }
    
    def _calculate_flow_direction(self, surface: Surface3D, point: TerrainPoint) -> float:
        """计算流向"""
        # 计算8个方向的坡度，选择最陡的方向
        directions = [
            (1, 0), (1, 1), (0, 1), (-1, 1),
            (-1, 0), (-1, -1), (0, -1), (1, -1)
        ]
        
        max_slope = 0
        flow_direction = 0
        
        current_elevation = self.spatial_analyzer._interpolate_elevation(surface, point.x, point.y)
        
        for i, (dx, dy) in enumerate(directions):
            neighbor_x = point.x + dx * 5.0  # 5米间距
            neighbor_y = point.y + dy * 5.0
            neighbor_elevation = self.spatial_analyzer._interpolate_elevation(surface, neighbor_x, neighbor_y)
            
            slope = (current_elevation - neighbor_elevation) / 5.0
            if slope > max_slope:
                max_slope = slope
                flow_direction = i * 45  # 每个方向45度
        
        return flow_direction
    
    def _flows_to_point(self, start: TerrainPoint, target: TerrainPoint, flow_direction: float) -> bool:
        """简化判断：检查流向是否大致指向目标点"""
        # 计算从起点到目标点的方向
        dx = target.x - start.x
        dy = target.y - start.y
        target_direction = math.degrees(math.atan2(dy, dx))
        
        if target_direction < 0:
            target_direction += 360
        
        # 检查流向是否在目标方向的±45度范围内
        angle_diff = abs(flow_direction - target_direction)
        if angle_diff > 180:
            angle_diff = 360 - angle_diff
        
        return angle_diff <= 45


# 工厂函数
def create_spatial_analyzer() -> SpatialAnalyzer:
    """创建空间分析器"""
    return SpatialAnalyzer()


def create_terrain_analyzer() -> TerrainAnalyzer:
    """创建地形分析器"""
    return TerrainAnalyzer()


def analyze_buffer_zone(points: List[TerrainPoint], radius: float) -> AnalysisResult:
    """缓冲区分析的便捷函数"""
    analyzer = create_spatial_analyzer()
    data = {'points': points, 'radius': radius}
    return analyzer.analyze(AnalysisType.BUFFER, data)


def analyze_slope(surface: Surface3D, grid_size: float = 10.0) -> AnalysisResult:
    """坡度分析的便捷函数"""
    analyzer = create_spatial_analyzer()
    data = {'surface': surface, 'grid_size': grid_size}
    return analyzer.analyze(AnalysisType.SLOPE, data)


def analyze_visibility(surface: Surface3D, observer_point: TerrainPoint,
                      observer_height: float = 1.7, max_distance: float = 1000.0) -> AnalysisResult:
    """可视性分析的便捷函数"""
    analyzer = create_spatial_analyzer()
    data = {
        'surface': surface,
        'observer_point': observer_point,
        'observer_height': observer_height,
        'max_distance': max_distance
    }
    return analyzer.analyze(AnalysisType.VISIBILITY, data)