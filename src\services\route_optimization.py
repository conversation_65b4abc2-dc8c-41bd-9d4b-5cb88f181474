"""
运输路线优化引擎
Transportation Route Optimization Engine
"""

import math
import heapq
from typing import List, Dict, Any, Optional, Tuple, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import time

# 尝试导入模型
try:
    from src.models.analysis_results import (
        OptimizationResult, OptimalRoute, Route, RoutePoint,
        OptimizationConstraints, CostComparison, PerformanceMetrics,
        OptimizationObjective
    )
    MODELS_AVAILABLE = True
except ImportError:
    MODELS_AVAILABLE = False
    # 定义简化的枚举类型
    class OptimizationObjective(str, Enum):
        DISTANCE = "distance"
        COST = "cost"
        TIME = "time"
        FUEL = "fuel"
        SAFETY = "safety"
        ENVIRONMENTAL = "environmental"

from src.services.surface_modeling import Surface3D, TerrainPoint


@dataclass
class Node:
    """路径节点"""
    id: str
    x: float
    y: float
    z: float = 0.0
    node_type: str = "waypoint"  # waypoint, intersection, facility
    properties: Dict[str, Any] = field(default_factory=dict)
    
    def distance_to(self, other: 'Node') -> float:
        """计算到另一节点的距离"""
        return math.sqrt(
            (self.x - other.x)**2 + 
            (self.y - other.y)**2 + 
            (self.z - other.z)**2
        )


@dataclass
class Edge:
    """路径边"""
    from_node: str
    to_node: str
    distance: float
    travel_time: float
    cost: float
    grade: float = 0.0
    surface_type: str = "paved"
    capacity: float = float('inf')
    restrictions: List[str] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PathSearchResult:
    """路径搜索结果"""
    success: bool
    path: List[str] = field(default_factory=list)
    total_distance: float = 0.0
    total_time: float = 0.0
    total_cost: float = 0.0
    nodes_explored: int = 0
    computation_time: float = 0.0
    message: str = ""


class Graph:
    """路径图"""
    
    def __init__(self):
        self.nodes: Dict[str, Node] = {}
        self.edges: Dict[str, List[Edge]] = {}
        self.reverse_edges: Dict[str, List[Edge]] = {}
    
    def add_node(self, node: Node):
        """添加节点"""
        self.nodes[node.id] = node
        if node.id not in self.edges:
            self.edges[node.id] = []
        if node.id not in self.reverse_edges:
            self.reverse_edges[node.id] = []
    
    def add_edge(self, edge: Edge):
        """添加边"""
        if edge.from_node not in self.edges:
            self.edges[edge.from_node] = []
        if edge.to_node not in self.reverse_edges:
            self.reverse_edges[edge.to_node] = []
        
        self.edges[edge.from_node].append(edge)
        self.reverse_edges[edge.to_node].append(edge)
    
    def get_neighbors(self, node_id: str) -> List[Edge]:
        """获取邻接边"""
        return self.edges.get(node_id, [])
    
    def get_node(self, node_id: str) -> Optional[Node]:
        """获取节点"""
        return self.nodes.get(node_id)


class AStarPathfinder:
    """A*路径搜索算法"""
    
    def __init__(self, graph: Graph):
        self.graph = graph
    
    def find_path(self, start_id: str, goal_id: str, 
                  objective: OptimizationObjective = OptimizationObjective.DISTANCE,
                  constraints: Optional[Dict[str, Any]] = None) -> PathSearchResult:
        """使用A*算法查找路径"""
        start_time = time.time()
        
        if start_id not in self.graph.nodes or goal_id not in self.graph.nodes:
            return PathSearchResult(
                success=False,
                message="起点或终点不存在"
            )
        
        start_node = self.graph.nodes[start_id]
        goal_node = self.graph.nodes[goal_id]
        
        # 初始化
        open_set = [(0, start_id)]
        came_from = {}
        g_score = {start_id: 0}
        f_score = {start_id: self._heuristic(start_node, goal_node, objective)}
        
        nodes_explored = 0
        
        while open_set:
            current_f, current_id = heapq.heappop(open_set)
            nodes_explored += 1
            
            if current_id == goal_id:
                # 重构路径
                path = self._reconstruct_path(came_from, current_id)
                path_info = self._calculate_path_metrics(path, objective)
                
                return PathSearchResult(
                    success=True,
                    path=path,
                    total_distance=path_info['distance'],
                    total_time=path_info['time'],
                    total_cost=path_info['cost'],
                    nodes_explored=nodes_explored,
                    computation_time=time.time() - start_time,
                    message="路径搜索成功"
                )
            
            # 探索邻居节点
            for edge in self.graph.get_neighbors(current_id):
                neighbor_id = edge.to_node
                
                # 检查约束条件
                if constraints and not self._check_constraints(edge, constraints):
                    continue
                
                tentative_g = g_score[current_id] + self._get_edge_cost(edge, objective)
                
                if neighbor_id not in g_score or tentative_g < g_score[neighbor_id]:
                    came_from[neighbor_id] = current_id
                    g_score[neighbor_id] = tentative_g
                    
                    neighbor_node = self.graph.nodes[neighbor_id]
                    h_cost = self._heuristic(neighbor_node, goal_node, objective)
                    f_score[neighbor_id] = tentative_g + h_cost
                    
                    heapq.heappush(open_set, (f_score[neighbor_id], neighbor_id))
        
        return PathSearchResult(
            success=False,
            nodes_explored=nodes_explored,
            computation_time=time.time() - start_time,
            message="未找到可行路径"
        )
    
    def _heuristic(self, node1: Node, node2: Node, 
                   objective: OptimizationObjective) -> float:
        """启发式函数"""
        distance = node1.distance_to(node2)
        
        if objective == OptimizationObjective.DISTANCE:
            return distance
        elif objective == OptimizationObjective.TIME:
            # 假设平均速度40km/h
            return distance / (40 / 3.6)  # 转换为m/s
        elif objective == OptimizationObjective.COST:
            # 简化的成本估算
            return distance * 0.5  # 每米0.5元
        else:
            return distance
    
    def _get_edge_cost(self, edge: Edge, objective: OptimizationObjective) -> float:
        """获取边的代价"""
        if objective == OptimizationObjective.DISTANCE:
            return edge.distance
        elif objective == OptimizationObjective.TIME:
            return edge.travel_time
        elif objective == OptimizationObjective.COST:
            return edge.cost
        else:
            return edge.distance
    
    def _check_constraints(self, edge: Edge, constraints: Dict[str, Any]) -> bool:
        """检查约束条件"""
        # 检查最大坡度
        if 'max_grade' in constraints:
            if abs(edge.grade) > constraints['max_grade']:
                return False
        
        # 检查车辆限制
        if 'vehicle_restrictions' in constraints:
            for restriction in constraints['vehicle_restrictions']:
                if restriction in edge.restrictions:
                    return False
        
        # 检查容量限制
        if 'min_capacity' in constraints:
            if edge.capacity < constraints['min_capacity']:
                return False
        
        return True
    
    def _reconstruct_path(self, came_from: Dict[str, str], current: str) -> List[str]:
        """重构路径"""
        path = [current]
        while current in came_from:
            current = came_from[current]
            path.append(current)
        return list(reversed(path))
    
    def _calculate_path_metrics(self, path: List[str], 
                               objective: OptimizationObjective) -> Dict[str, float]:
        """计算路径指标"""
        total_distance = 0.0
        total_time = 0.0
        total_cost = 0.0
        
        for i in range(len(path) - 1):
            from_id = path[i]
            to_id = path[i + 1]
            
            # 查找对应的边
            for edge in self.graph.get_neighbors(from_id):
                if edge.to_node == to_id:
                    total_distance += edge.distance
                    total_time += edge.travel_time
                    total_cost += edge.cost
                    break
        
        return {
            'distance': total_distance,
            'time': total_time,
            'cost': total_cost
        }


class DijkstraPathfinder:
    """Dijkstra路径搜索算法"""
    
    def __init__(self, graph: Graph):
        self.graph = graph
    
    def find_shortest_paths(self, start_id: str, 
                           objective: OptimizationObjective = OptimizationObjective.DISTANCE) -> Dict[str, PathSearchResult]:
        """查找从起点到所有其他点的最短路径"""
        start_time = time.time()
        
        if start_id not in self.graph.nodes:
            return {}
        
        # 初始化
        distances = {node_id: float('inf') for node_id in self.graph.nodes}
        distances[start_id] = 0
        came_from = {}
        visited = set()
        
        # 优先队列
        pq = [(0, start_id)]
        nodes_explored = 0
        
        while pq:
            current_dist, current_id = heapq.heappop(pq)
            
            if current_id in visited:
                continue
            
            visited.add(current_id)
            nodes_explored += 1
            
            # 探索邻居
            for edge in self.graph.get_neighbors(current_id):
                neighbor_id = edge.to_node
                
                if neighbor_id in visited:
                    continue
                
                edge_cost = self._get_edge_cost(edge, objective)
                new_distance = current_dist + edge_cost
                
                if new_distance < distances[neighbor_id]:
                    distances[neighbor_id] = new_distance
                    came_from[neighbor_id] = current_id
                    heapq.heappush(pq, (new_distance, neighbor_id))
        
        # 构建结果
        results = {}
        computation_time = time.time() - start_time
        
        for node_id in self.graph.nodes:
            if distances[node_id] != float('inf'):
                path = self._reconstruct_path(came_from, node_id, start_id)
                path_info = self._calculate_path_metrics(path, objective)
                
                results[node_id] = PathSearchResult(
                    success=True,
                    path=path,
                    total_distance=path_info['distance'],
                    total_time=path_info['time'],
                    total_cost=path_info['cost'],
                    nodes_explored=nodes_explored,
                    computation_time=computation_time,
                    message="路径搜索成功"
                )
        
        return results
    
    def _get_edge_cost(self, edge: Edge, objective: OptimizationObjective) -> float:
        """获取边的代价"""
        if objective == OptimizationObjective.DISTANCE:
            return edge.distance
        elif objective == OptimizationObjective.TIME:
            return edge.travel_time
        elif objective == OptimizationObjective.COST:
            return edge.cost
        else:
            return edge.distance
    
    def _reconstruct_path(self, came_from: Dict[str, str], 
                         end_id: str, start_id: str) -> List[str]:
        """重构路径"""
        if end_id == start_id:
            return [start_id]
        
        path = [end_id]
        current = end_id
        
        while current in came_from and current != start_id:
            current = came_from[current]
            path.append(current)
        
        return list(reversed(path))
    
    def _calculate_path_metrics(self, path: List[str], 
                               objective: OptimizationObjective) -> Dict[str, float]:
        """计算路径指标"""
        total_distance = 0.0
        total_time = 0.0
        total_cost = 0.0
        
        for i in range(len(path) - 1):
            from_id = path[i]
            to_id = path[i + 1]
            
            # 查找对应的边
            for edge in self.graph.get_neighbors(from_id):
                if edge.to_node == to_id:
                    total_distance += edge.distance
                    total_time += edge.travel_time
                    total_cost += edge.cost
                    break
        
        return {
            'distance': total_distance,
            'time': total_time,
            'cost': total_cost
        }


class DynamicPathfinder:
    """动态路径搜索（考虑实时条件）"""
    
    def __init__(self, graph: Graph):
        self.graph = graph
        self.traffic_conditions: Dict[str, float] = {}  # 边ID -> 拥堵系数
        self.road_conditions: Dict[str, str] = {}       # 边ID -> 路况状态
    
    def update_traffic_conditions(self, edge_conditions: Dict[str, float]):
        """更新交通状况"""
        self.traffic_conditions.update(edge_conditions)
    
    def update_road_conditions(self, road_conditions: Dict[str, str]):
        """更新路况信息"""
        self.road_conditions.update(road_conditions)
    
    def find_dynamic_path(self, start_id: str, goal_id: str,
                         objective: OptimizationObjective = OptimizationObjective.TIME) -> PathSearchResult:
        """考虑动态条件的路径搜索"""
        start_time = time.time()
        
        if start_id not in self.graph.nodes or goal_id not in self.graph.nodes:
            return PathSearchResult(
                success=False,
                message="起点或终点不存在"
            )
        
        # 使用修改后的A*算法
        start_node = self.graph.nodes[start_id]
        goal_node = self.graph.nodes[goal_id]
        
        open_set = [(0, start_id)]
        came_from = {}
        g_score = {start_id: 0}
        f_score = {start_id: self._dynamic_heuristic(start_node, goal_node, objective)}
        
        nodes_explored = 0
        
        while open_set:
            current_f, current_id = heapq.heappop(open_set)
            nodes_explored += 1
            
            if current_id == goal_id:
                path = self._reconstruct_path(came_from, current_id)
                path_info = self._calculate_dynamic_path_metrics(path, objective)
                
                return PathSearchResult(
                    success=True,
                    path=path,
                    total_distance=path_info['distance'],
                    total_time=path_info['time'],
                    total_cost=path_info['cost'],
                    nodes_explored=nodes_explored,
                    computation_time=time.time() - start_time,
                    message="动态路径搜索成功"
                )
            
            for edge in self.graph.get_neighbors(current_id):
                neighbor_id = edge.to_node
                
                # 计算动态代价
                dynamic_cost = self._get_dynamic_edge_cost(edge, objective)
                tentative_g = g_score[current_id] + dynamic_cost
                
                if neighbor_id not in g_score or tentative_g < g_score[neighbor_id]:
                    came_from[neighbor_id] = current_id
                    g_score[neighbor_id] = tentative_g
                    
                    neighbor_node = self.graph.nodes[neighbor_id]
                    h_cost = self._dynamic_heuristic(neighbor_node, goal_node, objective)
                    f_score[neighbor_id] = tentative_g + h_cost
                    
                    heapq.heappush(open_set, (f_score[neighbor_id], neighbor_id))
        
        return PathSearchResult(
            success=False,
            nodes_explored=nodes_explored,
            computation_time=time.time() - start_time,
            message="未找到可行的动态路径"
        )
    
    def _get_dynamic_edge_cost(self, edge: Edge, objective: OptimizationObjective) -> float:
        """获取考虑动态条件的边代价"""
        base_cost = edge.travel_time if objective == OptimizationObjective.TIME else edge.distance
        
        # 应用交通拥堵系数
        edge_key = f"{edge.from_node}-{edge.to_node}"
        traffic_factor = self.traffic_conditions.get(edge_key, 1.0)
        
        # 应用路况影响
        road_condition = self.road_conditions.get(edge_key, "good")
        condition_factor = {
            "excellent": 0.9,
            "good": 1.0,
            "fair": 1.2,
            "poor": 1.5,
            "closed": float('inf')
        }.get(road_condition, 1.0)
        
        return base_cost * traffic_factor * condition_factor
    
    def _dynamic_heuristic(self, node1: Node, node2: Node, 
                          objective: OptimizationObjective) -> float:
        """动态启发式函数"""
        distance = node1.distance_to(node2)
        
        if objective == OptimizationObjective.TIME:
            # 考虑平均交通状况
            avg_traffic_factor = sum(self.traffic_conditions.values()) / len(self.traffic_conditions) if self.traffic_conditions else 1.0
            return (distance / (40 / 3.6)) * avg_traffic_factor
        else:
            return distance
    
    def _reconstruct_path(self, came_from: Dict[str, str], current: str) -> List[str]:
        """重构路径"""
        path = [current]
        while current in came_from:
            current = came_from[current]
            path.append(current)
        return list(reversed(path))
    
    def _calculate_dynamic_path_metrics(self, path: List[str], 
                                       objective: OptimizationObjective) -> Dict[str, float]:
        """计算动态路径指标"""
        total_distance = 0.0
        total_time = 0.0
        total_cost = 0.0
        
        for i in range(len(path) - 1):
            from_id = path[i]
            to_id = path[i + 1]
            
            for edge in self.graph.get_neighbors(from_id):
                if edge.to_node == to_id:
                    total_distance += edge.distance
                    
                    # 计算动态时间和成本
                    dynamic_time = self._get_dynamic_edge_cost(edge, OptimizationObjective.TIME)
                    dynamic_cost = self._get_dynamic_edge_cost(edge, OptimizationObjective.COST)
                    
                    total_time += dynamic_time
                    total_cost += dynamic_cost
                    break
        
        return {
            'distance': total_distance,
            'time': total_time,
            'cost': total_cost
        }


# 工厂函数
def create_graph_from_terrain(terrain_surface: Surface3D, 
                             grid_spacing: float = 50.0) -> Graph:
    """从地形数据创建路径图"""
    graph = Graph()
    
    # 基于地形边界创建网格节点
    bounds = terrain_surface.bounds
    
    node_id = 0
    x = bounds.min_x
    while x <= bounds.max_x:
        y = bounds.min_y
        while y <= bounds.max_y:
            # 获取地面高程
            z = 0.0
            for triangle in terrain_surface.triangles:
                if triangle.contains_point(x, y):
                    z = triangle.interpolate_elevation(x, y)
                    break
            
            node = Node(
                id=f"node_{node_id}",
                x=x, y=y, z=z,
                node_type="waypoint"
            )
            graph.add_node(node)
            node_id += 1
            
            y += grid_spacing
        x += grid_spacing
    
    # 创建邻接边
    nodes_list = list(graph.nodes.values())
    for i, node1 in enumerate(nodes_list):
        for j, node2 in enumerate(nodes_list[i+1:], i+1):
            distance = node1.distance_to(node2)
            
            # 只连接相邻的节点
            if distance <= grid_spacing * 1.5:
                # 计算坡度
                horizontal_distance = math.sqrt((node2.x - node1.x)**2 + (node2.y - node1.y)**2)
                grade = abs(node2.z - node1.z) / horizontal_distance * 100 if horizontal_distance > 0 else 0
                
                # 估算行驶时间（基于距离和坡度）
                base_speed = 40  # km/h
                speed_factor = max(0.3, 1.0 - grade / 20.0)  # 坡度影响速度
                travel_time = distance / (base_speed * speed_factor / 3.6)
                
                # 估算成本
                cost = distance * (0.5 + grade * 0.01)  # 基础成本 + 坡度成本
                
                # 创建双向边
                edge1 = Edge(
                    from_node=node1.id,
                    to_node=node2.id,
                    distance=distance,
                    travel_time=travel_time,
                    cost=cost,
                    grade=grade
                )
                
                edge2 = Edge(
                    from_node=node2.id,
                    to_node=node1.id,
                    distance=distance,
                    travel_time=travel_time,
                    cost=cost,
                    grade=grade
                )
                
                graph.add_edge(edge1)
                graph.add_edge(edge2)
    
    return graph


def create_astar_pathfinder(graph: Graph) -> AStarPathfinder:
    """创建A*路径搜索器"""
    return AStarPathfinder(graph)


def create_dijkstra_pathfinder(graph: Graph) -> DijkstraPathfinder:
    """创建Dijkstra路径搜索器"""
    return DijkstraPathfinder(graph)


def create_dynamic_pathfinder(graph: Graph) -> DynamicPathfinder:
    """创建动态路径搜索器"""
    return DynamicPathfinder(graph)


def find_optimal_path(graph: Graph, start_id: str, goal_id: str,
                     objective: OptimizationObjective = OptimizationObjective.DISTANCE) -> PathSearchResult:
    """查找最优路径的便捷函数"""
    pathfinder = create_astar_pathfinder(graph)
    return pathfinder.find_path(start_id, goal_id, objective)