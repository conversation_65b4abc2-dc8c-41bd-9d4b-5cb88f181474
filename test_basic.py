"""
基础架构测试
Basic Infrastructure Test
"""

def test_imports():
    """测试基础模块导入"""
    try:
        from src.core.config import settings
        print("✅ 配置模块导入成功")
        print(f"   应用名称: {settings.APP_NAME}")
        print(f"   版本: {settings.VERSION}")
        print(f"   调试模式: {settings.DEBUG}")
        
        from src.core.database import Base, engine
        print("✅ 数据库模块导入成功")
        
        print("✅ 基础架构测试通过")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


def test_directory_structure():
    """测试目录结构"""
    import os
    
    required_dirs = [
        "src",
        "src/core", 
        "src/api",
        "src/api/v1",
        "src/tasks",
        "tests",
        "uploads",
        "logs",
        "config/design_standards"
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ 目录存在: {dir_path}")
        else:
            print(f"❌ 目录缺失: {dir_path}")
            return False
    
    print("✅ 目录结构测试通过")
    return True


def test_config_files():
    """测试配置文件"""
    import os
    
    config_files = [
        "requirements.txt",
        ".env.example", 
        "Dockerfile",
        "docker-compose.yml",
        "pytest.ini"
    ]
    
    for file_path in config_files:
        if os.path.exists(file_path):
            print(f"✅ 配置文件存在: {file_path}")
        else:
            print(f"❌ 配置文件缺失: {file_path}")
            return False
    
    print("✅ 配置文件测试通过")
    return True


if __name__ == "__main__":
    print("开始基础架构测试...")
    print("=" * 50)
    
    success = True
    success &= test_directory_structure()
    success &= test_config_files()
    success &= test_imports()
    
    print("=" * 50)
    if success:
        print("🎉 所有基础架构测试通过！")
        print("项目基础架构搭建完成")
    else:
        print("❌ 部分测试失败，请检查配置")