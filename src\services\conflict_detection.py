"""
冲突检测和安全分析系统
Conflict Detection and Safety Analysis System
"""

import math
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum

# 尝试导入模型
try:
    from src.models.analysis_results import (
        ConflictReport, ConflictItem, ConflictType, ConflictSeverity,
        SafetyReport, SafetyParameter, SafetyAnalysisSection
    )
    from src.models.road_design import RoadDesign, RoadAlignment, CrossSection
    MODELS_AVAILABLE = True
except ImportError:
    MODELS_AVAILABLE = False
    # 定义简化的枚举类型
    class ConflictType(str, Enum):
        GEOMETRIC = "geometric"
        SAFETY = "safety"
        STANDARD = "standard"
        ENVIRONMENTAL = "environmental"
        OPERATIONAL = "operational"
    
    class ConflictSeverity(str, Enum):
        LOW = "low"
        MEDIUM = "medium"
        HIGH = "high"
        CRITICAL = "critical"

from src.core.design_standards import MiningRoadStandards, RoadClass
from src.services.surface_modeling import Surface3D, TerrainPoint


@dataclass
class ConflictLocation:
    """冲突位置"""
    station: float
    x: float
    y: float
    z: Optional[float] = None
    description: str = ""


@dataclass
class GeometricConflict:
    """几何冲突"""
    conflict_id: str
    conflict_type: ConflictType
    severity: ConflictSeverity
    location: ConflictLocation
    description: str
    affected_elements: List[str]
    parameters: Dict[str, float]
    recommendations: List[str]


@dataclass
class SafetyIssue:
    """安全问题"""
    issue_id: str
    issue_type: str
    severity: ConflictSeverity
    location: ConflictLocation
    description: str
    standard_value: float
    actual_value: float
    deviation: float
    recommendations: List[str]


class ConflictDetector:
    """冲突检测器"""
    
    def __init__(self, design_standards: Optional[MiningRoadStandards] = None):
        self.standards = design_standards or MiningRoadStandards()
        self.conflict_id_counter = 0
    
    def detect_geometric_conflicts(self, road_design: Dict[str, Any], 
                                 terrain_surface: Optional[Surface3D] = None) -> List[GeometricConflict]:
        """检测几何冲突"""
        conflicts = []
        
        # 检查水平线形冲突
        if 'alignment' in road_design:
            alignment = road_design['alignment']
            conflicts.extend(self._check_horizontal_alignment_conflicts(alignment))
            conflicts.extend(self._check_vertical_alignment_conflicts(alignment))
        
        # 检查横断面冲突
        if 'cross_sections' in road_design:
            cross_sections = road_design['cross_sections']
            conflicts.extend(self._check_cross_section_conflicts(cross_sections))
        
        # 检查与地形的冲突
        if terrain_surface and 'alignment' in road_design:
            conflicts.extend(self._check_terrain_conflicts(road_design['alignment'], terrain_surface))
        
        # 检查设施冲突
        if 'facilities' in road_design:
            facilities = road_design['facilities']
            conflicts.extend(self._check_facility_conflicts(facilities))
        
        return conflicts
    
    def _check_horizontal_alignment_conflicts(self, alignment: Dict[str, Any]) -> List[GeometricConflict]:
        """检查水平线形冲突"""
        conflicts = []
        
        if 'horizontal_elements' not in alignment:
            return conflicts
        
        elements = alignment['horizontal_elements']
        road_class = alignment.get('road_class', RoadClass.MAIN_HAUL)
        geometric_standards = self.standards.get_geometric_standards(road_class)
        
        if not geometric_standards:
            return conflicts
        
        for i, element in enumerate(elements):
            element_type = element.get('type', '')
            
            # 检查圆曲线半径
            if element_type == 'circular':
                radius = element.get('radius', 0)
                min_radius = geometric_standards.min_horizontal_radius
                
                if radius < min_radius:
                    conflict = GeometricConflict(
                        conflict_id=self._generate_conflict_id(),
                        conflict_type=ConflictType.GEOMETRIC,
                        severity=ConflictSeverity.HIGH if radius < min_radius * 0.8 else ConflictSeverity.MEDIUM,
                        location=ConflictLocation(
                            station=element.get('start_station', 0),
                            x=element.get('center', [0, 0])[0],
                            y=element.get('center', [0, 0])[1],
                            description=f"水平曲线半径不足"
                        ),
                        description=f"水平曲线半径{radius:.1f}m小于标准要求{min_radius:.1f}m",
                        affected_elements=[f"horizontal_element_{i}"],
                        parameters={'actual_radius': radius, 'required_radius': min_radius},
                        recommendations=[
                            f"增大曲线半径至{min_radius:.1f}m以上",
                            "考虑降低设计速度",
                            "增加超高以补偿半径不足"
                        ]
                    )
                    conflicts.append(conflict)
            
            # 检查曲线长度
            if element_type in ['circular', 'spiral']:
                length = element.get('length', 0)
                min_length = geometric_standards.min_curve_length
                
                if length < min_length:
                    conflict = GeometricConflict(
                        conflict_id=self._generate_conflict_id(),
                        conflict_type=ConflictType.GEOMETRIC,
                        severity=ConflictSeverity.MEDIUM,
                        location=ConflictLocation(
                            station=element.get('start_station', 0),
                            x=element.get('start_point', [0, 0])[0],
                            y=element.get('start_point', [0, 0])[1],
                            description=f"曲线长度不足"
                        ),
                        description=f"曲线长度{length:.1f}m小于最小要求{min_length:.1f}m",
                        affected_elements=[f"horizontal_element_{i}"],
                        parameters={'actual_length': length, 'required_length': min_length},
                        recommendations=[
                            f"增加曲线长度至{min_length:.1f}m以上",
                            "调整线形设计以满足最小长度要求"
                        ]
                    )
                    conflicts.append(conflict)
        
        # 检查线形连续性
        conflicts.extend(self._check_alignment_continuity(elements))
        
        return conflicts
    
    def _check_vertical_alignment_conflicts(self, alignment: Dict[str, Any]) -> List[GeometricConflict]:
        """检查竖直线形冲突"""
        conflicts = []
        
        if 'vertical_elements' not in alignment:
            return conflicts
        
        elements = alignment['vertical_elements']
        road_class = alignment.get('road_class', RoadClass.MAIN_HAUL)
        geometric_standards = self.standards.get_geometric_standards(road_class)
        safety_standards = self.standards.get_safety_standards(road_class)
        
        if not geometric_standards or not safety_standards:
            return conflicts
        
        continuous_grade_length = 0.0
        
        for i, element in enumerate(elements):
            element_type = element.get('type', '')
            
            # 检查纵坡
            if element_type == 'grade':
                grade = abs(element.get('grade', 0))
                length = element.get('length', 0)
                
                # 检查最大坡度
                if grade > geometric_standards.max_grade:
                    conflict = GeometricConflict(
                        conflict_id=self._generate_conflict_id(),
                        conflict_type=ConflictType.GEOMETRIC,
                        severity=ConflictSeverity.HIGH,
                        location=ConflictLocation(
                            station=element.get('start_station', 0),
                            x=0, y=0,  # 竖直线形位置需要从水平线形计算
                            description=f"纵坡过大"
                        ),
                        description=f"纵坡{grade:.1f}%超过最大允许值{geometric_standards.max_grade:.1f}%",
                        affected_elements=[f"vertical_element_{i}"],
                        parameters={'actual_grade': grade, 'max_grade': geometric_standards.max_grade},
                        recommendations=[
                            f"降低纵坡至{geometric_standards.max_grade:.1f}%以下",
                            "考虑增加竖曲线缓解坡度变化",
                            "分段设计以避免长距离大坡度"
                        ]
                    )
                    conflicts.append(conflict)
                
                # 检查最小坡度（排水要求）
                if grade < geometric_standards.min_grade:
                    conflict = GeometricConflict(
                        conflict_id=self._generate_conflict_id(),
                        conflict_type=ConflictType.GEOMETRIC,
                        severity=ConflictSeverity.MEDIUM,
                        location=ConflictLocation(
                            station=element.get('start_station', 0),
                            x=0, y=0,
                            description=f"纵坡过小"
                        ),
                        description=f"纵坡{grade:.1f}%小于最小要求{geometric_standards.min_grade:.1f}%",
                        affected_elements=[f"vertical_element_{i}"],
                        parameters={'actual_grade': grade, 'min_grade': geometric_standards.min_grade},
                        recommendations=[
                            f"增加纵坡至{geometric_standards.min_grade:.1f}%以上",
                            "设置横向排水设施",
                            "考虑路面材料的排水性能"
                        ]
                    )
                    conflicts.append(conflict)
                
                # 累计连续坡长
                if grade > 3.0:  # 大于3%的坡度
                    continuous_grade_length += length
                else:
                    continuous_grade_length = 0.0
                
                # 检查连续坡长
                if continuous_grade_length > safety_standards.max_continuous_grade_length:
                    conflict = GeometricConflict(
                        conflict_id=self._generate_conflict_id(),
                        conflict_type=ConflictType.SAFETY,
                        severity=ConflictSeverity.HIGH,
                        location=ConflictLocation(
                            station=element.get('start_station', 0),
                            x=0, y=0,
                            description=f"连续坡长过长"
                        ),
                        description=f"连续坡长{continuous_grade_length:.1f}m超过限制{safety_standards.max_continuous_grade_length:.1f}m",
                        affected_elements=[f"vertical_element_{i}"],
                        parameters={
                            'continuous_length': continuous_grade_length,
                            'max_length': safety_standards.max_continuous_grade_length
                        },
                        recommendations=[
                            "设置缓坡段中断连续坡长",
                            "增加避险车道",
                            "考虑分段设计"
                        ]
                    )
                    conflicts.append(conflict)
            
            # 检查竖曲线半径
            elif element_type == 'vertical_curve':
                radius = element.get('radius', 0)
                curve_type = element.get('curve_type', 'crest')
                
                min_radius = (geometric_standards.min_vertical_radius_crest 
                            if curve_type == 'crest' 
                            else geometric_standards.min_vertical_radius_sag)
                
                if radius < min_radius:
                    conflict = GeometricConflict(
                        conflict_id=self._generate_conflict_id(),
                        conflict_type=ConflictType.GEOMETRIC,
                        severity=ConflictSeverity.HIGH,
                        location=ConflictLocation(
                            station=element.get('pvi_station', 0),
                            x=0, y=0,
                            description=f"{curve_type}竖曲线半径不足"
                        ),
                        description=f"{curve_type}竖曲线半径{radius:.1f}m小于最小要求{min_radius:.1f}m",
                        affected_elements=[f"vertical_element_{i}"],
                        parameters={'actual_radius': radius, 'required_radius': min_radius},
                        recommendations=[
                            f"增大竖曲线半径至{min_radius:.1f}m以上",
                            "调整前后坡度以减小坡度差",
                            "检查视距是否满足要求"
                        ]
                    )
                    conflicts.append(conflict)
        
        return conflicts
    
    def _check_cross_section_conflicts(self, cross_sections: List[Dict[str, Any]]) -> List[GeometricConflict]:
        """检查横断面冲突"""
        conflicts = []
        
        for i, section in enumerate(cross_sections):
            station = section.get('station', 0)
            
            # 检查路面宽度
            road_width = section.get('road_width', 0)
            if road_width < 6.0:  # 最小路面宽度
                conflict = GeometricConflict(
                    conflict_id=self._generate_conflict_id(),
                    conflict_type=ConflictType.GEOMETRIC,
                    severity=ConflictSeverity.HIGH,
                    location=ConflictLocation(
                        station=station,
                        x=0, y=0,
                        description=f"K{station/1000:.3f}路面宽度不足"
                    ),
                    description=f"路面宽度{road_width:.1f}m小于最小要求6.0m",
                    affected_elements=[f"cross_section_{i}"],
                    parameters={'actual_width': road_width, 'required_width': 6.0},
                    recommendations=[
                        "增加路面宽度至6.0m以上",
                        "考虑设置会车道",
                        "检查车辆通行要求"
                    ]
                )
                conflicts.append(conflict)
            
            # 检查边坡稳定性
            cut_slope = section.get('cut_slope_ratio', 0)
            fill_slope = section.get('fill_slope_ratio', 0)
            
            if cut_slope > 0 and cut_slope < 1.0:  # 挖方边坡过陡
                conflict = GeometricConflict(
                    conflict_id=self._generate_conflict_id(),
                    conflict_type=ConflictType.SAFETY,
                    severity=ConflictSeverity.HIGH,
                    location=ConflictLocation(
                        station=station,
                        x=0, y=0,
                        description=f"K{station/1000:.3f}挖方边坡过陡"
                    ),
                    description=f"挖方边坡坡率1:{cut_slope:.1f}过陡，建议不小于1:1.5",
                    affected_elements=[f"cross_section_{i}"],
                    parameters={'actual_slope': cut_slope, 'recommended_slope': 1.5},
                    recommendations=[
                        "放缓挖方边坡至1:1.5以上",
                        "考虑设置挡土墙",
                        "进行边坡稳定性分析"
                    ]
                )
                conflicts.append(conflict)
            
            if fill_slope > 0 and fill_slope < 1.2:  # 填方边坡过陡
                conflict = GeometricConflict(
                    conflict_id=self._generate_conflict_id(),
                    conflict_type=ConflictType.SAFETY,
                    severity=ConflictSeverity.HIGH,
                    location=ConflictLocation(
                        station=station,
                        x=0, y=0,
                        description=f"K{station/1000:.3f}填方边坡过陡"
                    ),
                    description=f"填方边坡坡率1:{fill_slope:.1f}过陡，建议不小于1:1.5",
                    affected_elements=[f"cross_section_{i}"],
                    parameters={'actual_slope': fill_slope, 'recommended_slope': 1.5},
                    recommendations=[
                        "放缓填方边坡至1:1.5以上",
                        "考虑分级填筑",
                        "加强填方压实控制"
                    ]
                )
                conflicts.append(conflict)
        
        return conflicts
    
    def _check_terrain_conflicts(self, alignment: Dict[str, Any], 
                               terrain_surface: Surface3D) -> List[GeometricConflict]:
        """检查与地形的冲突"""
        conflicts = []
        
        # 检查道路与地形的交叉
        if 'horizontal_elements' in alignment:
            for i, element in enumerate(alignment['horizontal_elements']):
                # 简化检查：检查道路是否与陡峭地形冲突
                if element.get('type') == 'straight':
                    start_point = element.get('start_point', [0, 0])
                    end_point = element.get('end_point', [0, 0])
                    
                    # 检查沿线地形坡度
                    terrain_slope = self._calculate_terrain_slope(
                        terrain_surface, start_point, end_point
                    )
                    
                    if terrain_slope > 30.0:  # 地形坡度超过30度
                        conflict = GeometricConflict(
                            conflict_id=self._generate_conflict_id(),
                            conflict_type=ConflictType.ENVIRONMENTAL,
                            severity=ConflictSeverity.MEDIUM,
                            location=ConflictLocation(
                                station=element.get('start_station', 0),
                                x=start_point[0],
                                y=start_point[1],
                                description=f"道路穿越陡峭地形"
                            ),
                            description=f"道路穿越坡度{terrain_slope:.1f}°的陡峭地形",
                            affected_elements=[f"horizontal_element_{i}"],
                            parameters={'terrain_slope': terrain_slope},
                            recommendations=[
                                "考虑调整路线避开陡峭地形",
                                "增加挡土墙等支护结构",
                                "进行地质稳定性评估"
                            ]
                        )
                        conflicts.append(conflict)
        
        return conflicts
    
    def _check_facility_conflicts(self, facilities: List[Dict[str, Any]]) -> List[GeometricConflict]:
        """检查设施冲突"""
        conflicts = []
        
        # 检查设施间距
        for i in range(len(facilities) - 1):
            facility1 = facilities[i]
            facility2 = facilities[i + 1]
            
            station1 = facility1.get('station', 0)
            station2 = facility2.get('station', 0)
            distance = abs(station2 - station1)
            
            facility_type1 = facility1.get('type', '')
            facility_type2 = facility2.get('type', '')
            
            # 检查会车道间距
            if facility_type1 == 'passing_bay' and facility_type2 == 'passing_bay':
                min_spacing = 300.0  # 最小间距300m
                if distance < min_spacing:
                    conflict = GeometricConflict(
                        conflict_id=self._generate_conflict_id(),
                        conflict_type=ConflictType.OPERATIONAL,
                        severity=ConflictSeverity.MEDIUM,
                        location=ConflictLocation(
                            station=(station1 + station2) / 2,
                            x=0, y=0,
                            description=f"会车道间距过近"
                        ),
                        description=f"会车道间距{distance:.1f}m小于最小要求{min_spacing:.1f}m",
                        affected_elements=[f"facility_{i}", f"facility_{i+1}"],
                        parameters={'actual_spacing': distance, 'required_spacing': min_spacing},
                        recommendations=[
                            f"调整会车道位置，保持{min_spacing:.1f}m以上间距",
                            "合并相邻会车道",
                            "重新规划会车道布局"
                        ]
                    )
                    conflicts.append(conflict)
        
        return conflicts
    
    def _check_alignment_continuity(self, elements: List[Dict[str, Any]]) -> List[GeometricConflict]:
        """检查线形连续性"""
        conflicts = []
        
        for i in range(len(elements) - 1):
            current_element = elements[i]
            next_element = elements[i + 1]
            
            current_end = current_element.get('end_point', [0, 0])
            next_start = next_element.get('start_point', [0, 0])
            
            # 计算间距
            distance = math.sqrt(
                (current_end[0] - next_start[0])**2 + 
                (current_end[1] - next_start[1])**2
            )
            
            if distance > 0.1:  # 超过10cm的间隙
                conflict = GeometricConflict(
                    conflict_id=self._generate_conflict_id(),
                    conflict_type=ConflictType.GEOMETRIC,
                    severity=ConflictSeverity.HIGH,
                    location=ConflictLocation(
                        station=current_element.get('end_station', 0),
                        x=current_end[0],
                        y=current_end[1],
                        description=f"线形不连续"
                    ),
                    description=f"线形元素间存在{distance:.3f}m的间隙",
                    affected_elements=[f"horizontal_element_{i}", f"horizontal_element_{i+1}"],
                    parameters={'gap_distance': distance},
                    recommendations=[
                        "调整线形元素确保连续性",
                        "检查坐标计算是否正确",
                        "重新计算线形参数"
                    ]
                )
                conflicts.append(conflict)
        
        return conflicts
    
    def _calculate_terrain_slope(self, terrain_surface: Surface3D, 
                                start_point: List[float], end_point: List[float]) -> float:
        """计算地形坡度"""
        # 简化计算：沿直线采样计算平均坡度
        num_samples = 10
        slopes = []
        
        for i in range(num_samples - 1):
            t1 = i / (num_samples - 1)
            t2 = (i + 1) / (num_samples - 1)
            
            x1 = start_point[0] + t1 * (end_point[0] - start_point[0])
            y1 = start_point[1] + t1 * (end_point[1] - start_point[1])
            
            x2 = start_point[0] + t2 * (end_point[0] - start_point[0])
            y2 = start_point[1] + t2 * (end_point[1] - start_point[1])
            
            # 获取地面高程（简化处理）
            z1 = self._get_terrain_elevation(terrain_surface, x1, y1)
            z2 = self._get_terrain_elevation(terrain_surface, x2, y2)
            
            horizontal_distance = math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
            if horizontal_distance > 0:
                slope = math.degrees(math.atan(abs(z2 - z1) / horizontal_distance))
                slopes.append(slope)
        
        return sum(slopes) / len(slopes) if slopes else 0.0
    
    def _get_terrain_elevation(self, terrain_surface: Surface3D, x: float, y: float) -> float:
        """获取地形高程"""
        # 简化实现：查找包含该点的三角形并插值
        for triangle in terrain_surface.triangles:
            if triangle.contains_point(x, y):
                return triangle.interpolate_elevation(x, y)
        
        # 如果没有找到，返回默认值
        return 0.0
    
    def _generate_conflict_id(self) -> str:
        """生成冲突ID"""
        self.conflict_id_counter += 1
        return f"CONFLICT_{self.conflict_id_counter:04d}"


# 工厂函数
def create_conflict_detector(design_standards: Optional[MiningRoadStandards] = None) -> ConflictDetector:
    """创建冲突检测器"""
    return ConflictDetector(design_standards)


def detect_road_conflicts(road_design: Dict[str, Any], 
                         terrain_surface: Optional[Surface3D] = None) -> List[GeometricConflict]:
    """检测道路设计冲突的便捷函数"""
    detector = create_conflict_detector()
    return detector.detect_geometric_conflicts(road_design, terrain_surface)


class SafetyAnalyzer:
    """安全分析器"""
    
    def __init__(self, design_standards: Optional[MiningRoadStandards] = None):
        self.standards = design_standards or MiningRoadStandards()
        self.issue_id_counter = 0
    
    def analyze_safety_parameters(self, road_design: Dict[str, Any], 
                                road_class: RoadClass = RoadClass.MAIN_HAUL) -> List[SafetyIssue]:
        """分析安全参数"""
        safety_issues = []
        
        geometric_standards = self.standards.get_geometric_standards(road_class)
        safety_standards = self.standards.get_safety_standards(road_class)
        
        if not geometric_standards or not safety_standards:
            return safety_issues
        
        # 检查视距
        if 'alignment' in road_design:
            safety_issues.extend(self._check_sight_distance(
                road_design['alignment'], geometric_standards
            ))
        
        # 检查超高
        if 'alignment' in road_design:
            safety_issues.extend(self._check_superelevation(
                road_design['alignment'], geometric_standards
            ))
        
        # 检查会车设施
        if 'facilities' in road_design:
            safety_issues.extend(self._check_passing_facilities(
                road_design['facilities'], safety_standards
            ))
        
        # 检查护栏设置
        if 'safety_facilities' in road_design:
            safety_issues.extend(self._check_barrier_requirements(
                road_design['safety_facilities'], safety_standards
            ))
        
        # 检查排水设施
        if 'drainage' in road_design:
            safety_issues.extend(self._check_drainage_adequacy(
                road_design['drainage'], road_design.get('cross_sections', [])
            ))
        
        return safety_issues
    
    def _check_sight_distance(self, alignment: Dict[str, Any], 
                            geometric_standards) -> List[SafetyIssue]:
        """检查视距"""
        safety_issues = []
        
        if 'vertical_elements' not in alignment:
            return safety_issues
        
        required_stopping_distance = geometric_standards.stopping_sight_distance
        
        for i, element in enumerate(alignment['vertical_elements']):
            if element.get('type') == 'vertical_curve':
                station = element.get('pvi_station', 0)
                radius = element.get('radius', 0)
                curve_type = element.get('curve_type', 'crest')
                
                # 计算视距（简化公式）
                if curve_type == 'crest':
                    # 凸形竖曲线视距计算
                    h1 = 1.2  # 驾驶员眼高
                    h2 = 0.1  # 障碍物高度
                    
                    if radius > 0:
                        sight_distance = math.sqrt(2 * radius * (h1 + h2))
                    else:
                        sight_distance = 0
                    
                    if sight_distance < required_stopping_distance:
                        issue = SafetyIssue(
                            issue_id=self._generate_issue_id(),
                            issue_type="sight_distance",
                            severity=ConflictSeverity.HIGH,
                            location=ConflictLocation(
                                station=station,
                                x=0, y=0,
                                description=f"K{station/1000:.3f}停车视距不足"
                            ),
                            description=f"凸形竖曲线停车视距{sight_distance:.1f}m不足",
                            standard_value=required_stopping_distance,
                            actual_value=sight_distance,
                            deviation=sight_distance - required_stopping_distance,
                            recommendations=[
                                f"增大竖曲线半径以提供{required_stopping_distance:.1f}m停车视距",
                                "降低设计速度",
                                "设置警示标志"
                            ]
                        )
                        safety_issues.append(issue)
        
        return safety_issues
    
    def _check_superelevation(self, alignment: Dict[str, Any], 
                            geometric_standards) -> List[SafetyIssue]:
        """检查超高"""
        safety_issues = []
        
        if 'horizontal_elements' not in alignment:
            return safety_issues
        
        max_superelevation = geometric_standards.max_superelevation
        
        for i, element in enumerate(alignment['horizontal_elements']):
            if element.get('type') == 'circular':
                station = element.get('start_station', 0)
                radius = element.get('radius', 0)
                design_speed = alignment.get('design_speed', 40)  # km/h
                
                # 计算所需超高
                if radius > 0:
                    # 简化的超高计算公式
                    required_superelevation = min(
                        (design_speed ** 2) / (127 * radius) - 0.15,  # 减去横向摩擦系数
                        max_superelevation / 100
                    ) * 100
                    
                    actual_superelevation = element.get('superelevation', 0)
                    
                    if required_superelevation > max_superelevation:
                        issue = SafetyIssue(
                            issue_id=self._generate_issue_id(),
                            issue_type="superelevation",
                            severity=ConflictSeverity.MEDIUM,
                            location=ConflictLocation(
                                station=station,
                                x=element.get('center', [0, 0])[0],
                                y=element.get('center', [0, 0])[1],
                                description=f"K{station/1000:.3f}超高不足"
                            ),
                            description=f"圆曲线所需超高{required_superelevation:.1f}%超过最大允许值",
                            standard_value=max_superelevation,
                            actual_value=required_superelevation,
                            deviation=required_superelevation - max_superelevation,
                            recommendations=[
                                "增大曲线半径以减少所需超高",
                                "降低设计速度",
                                "检查横向摩擦系数是否合理"
                            ]
                        )
                        safety_issues.append(issue)
                    
                    elif abs(actual_superelevation - required_superelevation) > 1.0:
                        issue = SafetyIssue(
                            issue_id=self._generate_issue_id(),
                            issue_type="superelevation",
                            severity=ConflictSeverity.MEDIUM,
                            location=ConflictLocation(
                                station=station,
                                x=element.get('center', [0, 0])[0],
                                y=element.get('center', [0, 0])[1],
                                description=f"K{station/1000:.3f}超高设置不当"
                            ),
                            description=f"实际超高{actual_superelevation:.1f}%与所需超高{required_superelevation:.1f}%差异过大",
                            standard_value=required_superelevation,
                            actual_value=actual_superelevation,
                            deviation=actual_superelevation - required_superelevation,
                            recommendations=[
                                f"调整超高至{required_superelevation:.1f}%",
                                "检查超高渐变段设计",
                                "确保排水功能正常"
                            ]
                        )
                        safety_issues.append(issue)
        
        return safety_issues
    
    def _check_passing_facilities(self, facilities: List[Dict[str, Any]], 
                                safety_standards) -> List[SafetyIssue]:
        """检查会车设施"""
        safety_issues = []
        
        required_spacing = safety_standards.passing_bay_spacing
        required_length = safety_standards.passing_bay_length
        required_width = safety_standards.passing_bay_width
        
        passing_bays = [f for f in facilities if f.get('type') == 'passing_bay']
        
        # 检查会车道间距
        for i in range(len(passing_bays) - 1):
            current_bay = passing_bays[i]
            next_bay = passing_bays[i + 1]
            
            spacing = abs(next_bay.get('station', 0) - current_bay.get('station', 0))
            
            if spacing > required_spacing * 1.5:  # 间距过大
                issue = SafetyIssue(
                    issue_id=self._generate_issue_id(),
                    issue_type="passing_bay_spacing",
                    severity=ConflictSeverity.MEDIUM,
                    location=ConflictLocation(
                        station=(current_bay.get('station', 0) + next_bay.get('station', 0)) / 2,
                        x=0, y=0,
                        description=f"会车道间距过大"
                    ),
                    description=f"会车道间距{spacing:.1f}m超过推荐值{required_spacing * 1.5:.1f}m",
                    standard_value=required_spacing,
                    actual_value=spacing,
                    deviation=spacing - required_spacing,
                    recommendations=[
                        "增加中间会车道",
                        "重新评估交通量需求",
                        "考虑设置临时会车点"
                    ]
                )
                safety_issues.append(issue)
        
        # 检查会车道尺寸
        for i, bay in enumerate(passing_bays):
            station = bay.get('station', 0)
            length = bay.get('length', 0)
            width = bay.get('width', 0)
            
            if length < required_length:
                issue = SafetyIssue(
                    issue_id=self._generate_issue_id(),
                    issue_type="passing_bay_length",
                    severity=ConflictSeverity.MEDIUM,
                    location=ConflictLocation(
                        station=station,
                        x=0, y=0,
                        description=f"K{station/1000:.3f}会车道长度不足"
                    ),
                    description=f"会车道长度{length:.1f}m小于标准要求{required_length:.1f}m",
                    standard_value=required_length,
                    actual_value=length,
                    deviation=length - required_length,
                    recommendations=[
                        f"增加会车道长度至{required_length:.1f}m",
                        "确保车辆能够安全会车",
                        "检查视距是否充足"
                    ]
                )
                safety_issues.append(issue)
            
            if width < required_width:
                issue = SafetyIssue(
                    issue_id=self._generate_issue_id(),
                    issue_type="passing_bay_width",
                    severity=ConflictSeverity.MEDIUM,
                    location=ConflictLocation(
                        station=station,
                        x=0, y=0,
                        description=f"K{station/1000:.3f}会车道宽度不足"
                    ),
                    description=f"会车道宽度{width:.1f}m小于标准要求{required_width:.1f}m",
                    standard_value=required_width,
                    actual_value=width,
                    deviation=width - required_width,
                    recommendations=[
                        f"增加会车道宽度至{required_width:.1f}m",
                        "确保大型车辆能够通过",
                        "检查转弯半径是否足够"
                    ]
                )
                safety_issues.append(issue)
        
        return safety_issues
    
    def _check_barrier_requirements(self, safety_facilities: List[Dict[str, Any]], 
                                  safety_standards) -> List[SafetyIssue]:
        """检查护栏要求"""
        safety_issues = []
        
        required_height = safety_standards.barrier_height
        required_setback = safety_standards.barrier_setback
        
        barriers = [f for f in safety_facilities if f.get('type') == 'barrier']
        
        for i, barrier in enumerate(barriers):
            station = barrier.get('station', 0)
            height = barrier.get('height', 0)
            setback = barrier.get('setback', 0)
            
            # 检查护栏高度
            if height < required_height:
                issue = SafetyIssue(
                    issue_id=self._generate_issue_id(),
                    issue_type="barrier_height",
                    severity=ConflictSeverity.MEDIUM,
                    location=ConflictLocation(
                        station=station,
                        x=0, y=0,
                        description=f"K{station/1000:.3f}护栏高度不足"
                    ),
                    description=f"护栏高度{height:.1f}m小于标准要求{required_height:.1f}m",
                    standard_value=required_height,
                    actual_value=height,
                    deviation=height - required_height,
                    recommendations=[
                        f"增加护栏高度至{required_height:.1f}m",
                        "检查护栏强度是否足够",
                        "确保护栏连续性"
                    ]
                )
                safety_issues.append(issue)
            
            # 检查护栏距离
            if setback < required_setback:
                issue = SafetyIssue(
                    issue_id=self._generate_issue_id(),
                    issue_type="barrier_setback",
                    severity=ConflictSeverity.LOW,
                    location=ConflictLocation(
                        station=station,
                        x=0, y=0,
                        description=f"K{station/1000:.3f}护栏距离过近"
                    ),
                    description=f"护栏距路面边缘{setback:.1f}m小于标准要求{required_setback:.1f}m",
                    standard_value=required_setback,
                    actual_value=setback,
                    deviation=setback - required_setback,
                    recommendations=[
                        f"增加护栏距离至{required_setback:.1f}m",
                        "确保车辆侧向安全空间",
                        "检查护栏端部处理"
                    ]
                )
                safety_issues.append(issue)
        
        return safety_issues
    
    def _check_drainage_adequacy(self, drainage: Dict[str, Any], 
                               cross_sections: List[Dict[str, Any]]) -> List[SafetyIssue]:
        """检查排水充分性"""
        safety_issues = []
        
        # 检查边沟设计
        if 'side_ditches' in drainage:
            ditches = drainage['side_ditches']
            
            for i, ditch in enumerate(ditches):
                station = ditch.get('station', 0)
                depth = ditch.get('depth', 0)
                width = ditch.get('width', 0)
                slope = ditch.get('slope', 0)
                
                # 检查边沟深度
                if depth < 0.3:  # 最小深度30cm
                    issue = SafetyIssue(
                        issue_id=self._generate_issue_id(),
                        issue_type="ditch_depth",
                        severity=ConflictSeverity.MEDIUM,
                        location=ConflictLocation(
                            station=station,
                            x=0, y=0,
                            description=f"K{station/1000:.3f}边沟深度不足"
                        ),
                        description=f"边沟深度{depth:.1f}m小于最小要求0.3m",
                        standard_value=0.3,
                        actual_value=depth,
                        deviation=depth - 0.3,
                        recommendations=[
                            "增加边沟深度至0.3m以上",
                            "确保排水能力充足",
                            "检查沟底纵坡是否合理"
                        ]
                    )
                    safety_issues.append(issue)
                
                # 检查边沟坡度
                if slope < 0.3:  # 最小坡度0.3%
                    issue = SafetyIssue(
                        issue_id=self._generate_issue_id(),
                        issue_type="ditch_slope",
                        severity=ConflictSeverity.MEDIUM,
                        location=ConflictLocation(
                            station=station,
                            x=0, y=0,
                            description=f"K{station/1000:.3f}边沟坡度过小"
                        ),
                        description=f"边沟纵坡{slope:.1f}%小于最小要求0.3%",
                        standard_value=0.3,
                        actual_value=slope,
                        deviation=slope - 0.3,
                        recommendations=[
                            "增加边沟纵坡至0.3%以上",
                            "设置跌水井或急流槽",
                            "检查排水出口是否通畅"
                        ]
                    )
                    safety_issues.append(issue)
        
        # 检查路面排水
        for section in cross_sections:
            station = section.get('station', 0)
            crown_slope = section.get('crown_slope', 0)
            
            if crown_slope < 1.5:  # 路拱坡度小于1.5%
                issue = SafetyIssue(
                    issue_id=self._generate_issue_id(),
                    issue_type="crown_slope",
                    severity=ConflictSeverity.LOW,
                    location=ConflictLocation(
                        station=station,
                        x=0, y=0,
                        description=f"K{station/1000:.3f}路拱坡度过小"
                    ),
                    description=f"路拱坡度{crown_slope:.1f}%小于推荐值1.5%",
                    standard_value=1.5,
                    actual_value=crown_slope,
                    deviation=crown_slope - 1.5,
                    recommendations=[
                        "增加路拱坡度至1.5%以上",
                        "确保路面排水顺畅",
                        "检查路面材料渗透性"
                    ]
                )
                safety_issues.append(issue)
        
        return safety_issues
    
    def _generate_issue_id(self) -> str:
        """生成安全问题ID"""
        self.issue_id_counter += 1
        return f"SAFETY_{self.issue_id_counter:04d}"


class ComprehensiveAnalyzer:
    """综合分析器"""
    
    def __init__(self, design_standards: Optional[MiningRoadStandards] = None):
        self.conflict_detector = ConflictDetector(design_standards)
        self.safety_analyzer = SafetyAnalyzer(design_standards)
    
    def comprehensive_analysis(self, road_design: Dict[str, Any], 
                             terrain_surface: Optional[Surface3D] = None,
                             road_class: RoadClass = RoadClass.MAIN_HAUL) -> Dict[str, Any]:
        """综合分析"""
        # 几何冲突检测
        geometric_conflicts = self.conflict_detector.detect_geometric_conflicts(
            road_design, terrain_surface
        )
        
        # 安全参数检查
        safety_issues = self.safety_analyzer.analyze_safety_parameters(
            road_design, road_class
        )
        
        # 统计分析结果
        conflict_summary = self._summarize_conflicts(geometric_conflicts)
        safety_summary = self._summarize_safety_issues(safety_issues)
        
        # 生成综合报告
        report = {
            'analysis_summary': {
                'total_conflicts': len(geometric_conflicts),
                'total_safety_issues': len(safety_issues),
                'critical_issues': conflict_summary['critical'] + safety_summary['critical'],
                'high_issues': conflict_summary['high'] + safety_summary['high'],
                'medium_issues': conflict_summary['medium'] + safety_summary['medium'],
                'low_issues': conflict_summary['low'] + safety_summary['low']
            },
            'geometric_conflicts': geometric_conflicts,
            'safety_issues': safety_issues,
            'conflict_summary': conflict_summary,
            'safety_summary': safety_summary,
            'recommendations': self._generate_comprehensive_recommendations(
                geometric_conflicts, safety_issues
            )
        }
        
        return report
    
    def _summarize_conflicts(self, conflicts: List[GeometricConflict]) -> Dict[str, int]:
        """汇总冲突统计"""
        summary = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        
        for conflict in conflicts:
            severity = conflict.severity.lower()
            if severity in summary:
                summary[severity] += 1
        
        return summary
    
    def _summarize_safety_issues(self, issues: List[SafetyIssue]) -> Dict[str, int]:
        """汇总安全问题统计"""
        summary = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        
        for issue in issues:
            severity = issue.severity.lower()
            if severity in summary:
                summary[severity] += 1
        
        return summary
    
    def _generate_comprehensive_recommendations(self, conflicts: List[GeometricConflict], 
                                             issues: List[SafetyIssue]) -> List[str]:
        """生成综合建议"""
        recommendations = []
        
        # 基于冲突严重程度的建议
        critical_conflicts = [c for c in conflicts if c.severity == ConflictSeverity.CRITICAL]
        high_conflicts = [c for c in conflicts if c.severity == ConflictSeverity.HIGH]
        
        if critical_conflicts:
            recommendations.append("存在严重冲突，建议重新设计相关路段")
        
        if high_conflicts:
            recommendations.append("存在高级冲突，需要优先解决")
        
        # 基于冲突类型的建议
        geometric_conflicts = [c for c in conflicts if c.conflict_type == ConflictType.GEOMETRIC]
        safety_conflicts = [c for c in conflicts if c.conflict_type == ConflictType.SAFETY]
        
        if len(geometric_conflicts) > 5:
            recommendations.append("几何设计问题较多，建议全面检查设计参数")
        
        if len(safety_conflicts) > 3:
            recommendations.append("安全问题需要重点关注，建议增加安全设施")
        
        # 基于安全问题的建议
        critical_issues = [i for i in issues if i.severity == ConflictSeverity.CRITICAL]
        
        if critical_issues:
            recommendations.append("存在严重安全隐患，必须立即整改")
        
        if not recommendations:
            recommendations.append("设计总体符合要求，建议进行详细复核")
        
        return recommendations


# 扩展工厂函数
def create_safety_analyzer(design_standards: Optional[MiningRoadStandards] = None) -> SafetyAnalyzer:
    """创建安全分析器"""
    return SafetyAnalyzer(design_standards)


def create_comprehensive_analyzer(design_standards: Optional[MiningRoadStandards] = None) -> ComprehensiveAnalyzer:
    """创建综合分析器"""
    return ComprehensiveAnalyzer(design_standards)


def analyze_road_safety(road_design: Dict[str, Any], 
                       road_class: RoadClass = RoadClass.MAIN_HAUL) -> List[SafetyIssue]:
    """分析道路安全的便捷函数"""
    analyzer = create_safety_analyzer()
    return analyzer.analyze_safety_parameters(road_design, road_class)


def comprehensive_road_analysis(road_design: Dict[str, Any], 
                              terrain_surface: Optional[Surface3D] = None,
                              road_class: RoadClass = RoadClass.MAIN_HAUL) -> Dict[str, Any]:
    """综合道路分析的便捷函数"""
    analyzer = create_comprehensive_analyzer()
    return analyzer.comprehensive_analysis(road_design, terrain_surface, road_class)