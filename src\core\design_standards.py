"""
露天矿山道路设计标准
Open Pit Mining Road Design Standards
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import os


class RoadClass(str, Enum):
    """道路等级"""
    MAIN_HAUL = "main_haul"  # 主运输道路
    SECONDARY_HAUL = "secondary_haul"  # 次要运输道路
    ACCESS_ROAD = "access_road"  # 进场道路
    RAMP_ROAD = "ramp_road"  # 坡道
    BENCH_ROAD = "bench_road"  # 台阶道路


class VehicleType(str, Enum):
    """车辆类型"""
    HEAVY_TRUCK = "heavy_truck"  # 重型卡车 (>100t)
    MEDIUM_TRUCK = "medium_truck"  # 中型卡车 (50-100t)
    LIGHT_TRUCK = "light_truck"  # 轻型卡车 (<50t)
    EXCAVATOR = "excavator"  # 挖掘机
    AUXILIARY = "auxiliary"  # 辅助车辆


@dataclass
class GeometricStandards:
    """几何设计标准"""
    # 基本参数
    design_speed: float  # 设计速度 (km/h)
    lane_width: float  # 车道宽度 (m)
    shoulder_width: float  # 路肩宽度 (m)
    
    # 水平线形
    min_horizontal_radius: float  # 最小水平曲线半径 (m)
    max_superelevation: float  # 最大超高 (%)
    min_curve_length: float  # 最小曲线长度 (m)
    
    # 竖直线形
    max_grade: float  # 最大纵坡 (%)
    min_grade: float  # 最小纵坡 (%)
    min_vertical_radius_crest: float  # 凸形竖曲线最小半径 (m)
    min_vertical_radius_sag: float  # 凹形竖曲线最小半径 (m)
    
    # 视距
    stopping_sight_distance: float  # 停车视距 (m)
    passing_sight_distance: float  # 超车视距 (m)
    
    # 横断面
    crown_slope: float  # 路拱坡度 (%)
    shoulder_slope: float  # 路肩坡度 (%)
    cut_slope_ratio: float  # 挖方边坡坡率
    fill_slope_ratio: float  # 填方边坡坡率


@dataclass
class SafetyStandards:
    """安全设计标准"""
    # 护栏要求
    barrier_height: float  # 护栏高度 (m)
    barrier_setback: float  # 护栏距路面边缘距离 (m)
    
    # 安全净空
    vertical_clearance: float  # 垂直净空 (m)
    horizontal_clearance: float  # 水平净空 (m)
    
    # 会车要求
    passing_bay_spacing: float  # 会车道间距 (m)
    passing_bay_length: float  # 会车道长度 (m)
    passing_bay_width: float  # 会车道宽度 (m)
    
    # 坡道安全
    max_continuous_grade_length: float  # 最大连续坡长 (m)
    grade_change_rate: float  # 坡度变化率 (%/100m)


@dataclass
class EnvironmentalStandards:
    """环境保护标准"""
    # 排水要求
    min_ditch_depth: float  # 最小边沟深度 (m)
    min_ditch_width: float  # 最小边沟宽度 (m)
    max_ditch_slope: float  # 最大边沟坡度 (%)
    
    # 防尘要求
    dust_suppression_required: bool  # 是否需要防尘
    watering_frequency: float  # 洒水频率 (次/天)
    
    # 噪音控制
    max_noise_level: float  # 最大噪音水平 (dB)
    noise_barrier_required: bool  # 是否需要声屏障


class MiningRoadStandards:
    """露天矿山道路设计标准"""
    
    def __init__(self):
        self.standards = self._load_standards()
    
    def _load_standards(self) -> Dict[str, Dict[str, Any]]:
        """加载设计标准"""
        # 默认标准配置
        default_standards = {
            RoadClass.MAIN_HAUL: {
                "geometric": GeometricStandards(
                    design_speed=40.0,
                    lane_width=7.0,
                    shoulder_width=1.5,
                    min_horizontal_radius=60.0,
                    max_superelevation=6.0,
                    min_curve_length=30.0,
                    max_grade=8.0,
                    min_grade=0.5,
                    min_vertical_radius_crest=400.0,
                    min_vertical_radius_sag=300.0,
                    stopping_sight_distance=40.0,
                    passing_sight_distance=120.0,
                    crown_slope=2.0,
                    shoulder_slope=4.0,
                    cut_slope_ratio=1.5,
                    fill_slope_ratio=1.5
                ),
                "safety": SafetyStandards(
                    barrier_height=1.2,
                    barrier_setback=0.5,
                    vertical_clearance=5.0,
                    horizontal_clearance=1.0,
                    passing_bay_spacing=500.0,
                    passing_bay_length=50.0,
                    passing_bay_width=4.0,
                    max_continuous_grade_length=300.0,
                    grade_change_rate=2.0
                ),
                "environmental": EnvironmentalStandards(
                    min_ditch_depth=0.5,
                    min_ditch_width=1.0,
                    max_ditch_slope=5.0,
                    dust_suppression_required=True,
                    watering_frequency=3.0,
                    max_noise_level=85.0,
                    noise_barrier_required=False
                )
            },
            RoadClass.SECONDARY_HAUL: {
                "geometric": GeometricStandards(
                    design_speed=30.0,
                    lane_width=6.0,
                    shoulder_width=1.0,
                    min_horizontal_radius=45.0,
                    max_superelevation=6.0,
                    min_curve_length=25.0,
                    max_grade=10.0,
                    min_grade=0.5,
                    min_vertical_radius_crest=300.0,
                    min_vertical_radius_sag=200.0,
                    stopping_sight_distance=30.0,
                    passing_sight_distance=90.0,
                    crown_slope=2.0,
                    shoulder_slope=4.0,
                    cut_slope_ratio=1.5,
                    fill_slope_ratio=1.5
                ),
                "safety": SafetyStandards(
                    barrier_height=1.0,
                    barrier_setback=0.5,
                    vertical_clearance=4.5,
                    horizontal_clearance=0.8,
                    passing_bay_spacing=300.0,
                    passing_bay_length=40.0,
                    passing_bay_width=3.5,
                    max_continuous_grade_length=200.0,
                    grade_change_rate=2.5
                ),
                "environmental": EnvironmentalStandards(
                    min_ditch_depth=0.4,
                    min_ditch_width=0.8,
                    max_ditch_slope=6.0,
                    dust_suppression_required=True,
                    watering_frequency=2.0,
                    max_noise_level=80.0,
                    noise_barrier_required=False
                )
            },
            RoadClass.RAMP_ROAD: {
                "geometric": GeometricStandards(
                    design_speed=25.0,
                    lane_width=8.0,  # 坡道需要更宽
                    shoulder_width=1.0,
                    min_horizontal_radius=40.0,
                    max_superelevation=4.0,  # 坡道超高限制更严
                    min_curve_length=20.0,
                    max_grade=12.0,  # 坡道允许更大坡度
                    min_grade=1.0,
                    min_vertical_radius_crest=250.0,
                    min_vertical_radius_sag=150.0,
                    stopping_sight_distance=25.0,
                    passing_sight_distance=75.0,
                    crown_slope=2.0,
                    shoulder_slope=4.0,
                    cut_slope_ratio=1.25,  # 坡道边坡更缓
                    fill_slope_ratio=1.75
                ),
                "safety": SafetyStandards(
                    barrier_height=1.5,  # 坡道护栏更高
                    barrier_setback=0.5,
                    vertical_clearance=4.5,
                    horizontal_clearance=1.0,
                    passing_bay_spacing=200.0,  # 坡道会车道更密
                    passing_bay_length=60.0,
                    passing_bay_width=4.0,
                    max_continuous_grade_length=150.0,  # 坡道连续坡长限制更严
                    grade_change_rate=1.5
                ),
                "environmental": EnvironmentalStandards(
                    min_ditch_depth=0.6,
                    min_ditch_width=1.2,
                    max_ditch_slope=4.0,
                    dust_suppression_required=True,
                    watering_frequency=4.0,  # 坡道防尘要求更高
                    max_noise_level=85.0,
                    noise_barrier_required=True
                )
            }
        }
        
        # 尝试从配置文件加载
        config_path = "config/design_standards/mining_road_standards.json"
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并配置
                    for road_class, standards in loaded_config.items():
                        if road_class in default_standards:
                            default_standards[road_class].update(standards)
            except Exception as e:
                print(f"警告：无法加载配置文件 {config_path}: {e}")
        
        return default_standards
    
    def get_geometric_standards(self, road_class: RoadClass) -> GeometricStandards:
        """获取几何设计标准"""
        standards_dict = self.standards.get(road_class, {})
        geometric_dict = standards_dict.get("geometric")
        if isinstance(geometric_dict, GeometricStandards):
            return geometric_dict
        elif isinstance(geometric_dict, dict):
            return GeometricStandards(**geometric_dict)
        return None
    
    def get_safety_standards(self, road_class: RoadClass) -> SafetyStandards:
        """获取安全设计标准"""
        standards_dict = self.standards.get(road_class, {})
        safety_dict = standards_dict.get("safety")
        if isinstance(safety_dict, SafetyStandards):
            return safety_dict
        elif isinstance(safety_dict, dict):
            return SafetyStandards(**safety_dict)
        return None
    
    def get_environmental_standards(self, road_class: RoadClass) -> EnvironmentalStandards:
        """获取环境保护标准"""
        standards_dict = self.standards.get(road_class, {})
        env_dict = standards_dict.get("environmental")
        if isinstance(env_dict, EnvironmentalStandards):
            return env_dict
        elif isinstance(env_dict, dict):
            return EnvironmentalStandards(**env_dict)
        return None
    
    def validate_design_parameters(self, road_class: RoadClass, 
                                 parameters: Dict[str, float]) -> List[str]:
        """验证设计参数"""
        errors = []
        geometric = self.get_geometric_standards(road_class)
        safety = self.get_safety_standards(road_class)
        
        if not geometric or not safety:
            errors.append(f"未找到道路等级 {road_class} 的设计标准")
            return errors
        
        # 验证几何参数
        if "horizontal_radius" in parameters:
            radius = parameters["horizontal_radius"]
            if radius < geometric.min_horizontal_radius:
                errors.append(f"水平曲线半径 {radius}m 小于最小值 {geometric.min_horizontal_radius}m")
        
        if "grade" in parameters:
            grade = abs(parameters["grade"])
            if grade > geometric.max_grade:
                errors.append(f"纵坡 {grade}% 超过最大值 {geometric.max_grade}%")
            if grade < geometric.min_grade:
                errors.append(f"纵坡 {grade}% 小于最小值 {geometric.min_grade}%")
        
        if "superelevation" in parameters:
            superelevation = abs(parameters["superelevation"])
            if superelevation > geometric.max_superelevation:
                errors.append(f"超高 {superelevation}% 超过最大值 {geometric.max_superelevation}%")
        
        if "vertical_radius" in parameters:
            v_radius = parameters["vertical_radius"]
            if v_radius < geometric.min_vertical_radius_crest:
                errors.append(f"竖曲线半径 {v_radius}m 小于最小值 {geometric.min_vertical_radius_crest}m")
        
        # 验证安全参数
        if "continuous_grade_length" in parameters:
            length = parameters["continuous_grade_length"]
            if length > safety.max_continuous_grade_length:
                errors.append(f"连续坡长 {length}m 超过最大值 {safety.max_continuous_grade_length}m")
        
        return errors
    
    def calculate_minimum_radius(self, road_class: RoadClass, design_speed: Optional[float] = None) -> float:
        """计算最小曲线半径"""
        geometric = self.get_geometric_standards(road_class)
        if not geometric:
            return 30.0  # 默认值
        
        speed = design_speed or geometric.design_speed
        max_e = geometric.max_superelevation / 100.0  # 转换为小数
        max_f = 0.15  # 最大横向摩擦系数
        
        # R = V²/(127(e + f))
        min_radius = (speed * speed) / (127 * (max_e + max_f))
        
        return max(min_radius, geometric.min_horizontal_radius)
    
    def calculate_stopping_sight_distance(self, road_class: RoadClass, 
                                        design_speed: Optional[float] = None) -> float:
        """计算停车视距"""
        geometric = self.get_geometric_standards(road_class)
        if not geometric:
            return 30.0
        
        speed = design_speed or geometric.design_speed
        
        # 简化的停车视距计算
        # S = V²/(254f) + VT
        f = 0.35  # 纵向摩擦系数
        t = 2.5   # 反应时间 (秒)
        
        braking_distance = (speed * speed) / (254 * f)
        reaction_distance = speed * t / 3.6  # 转换为m/s
        
        calculated_distance = braking_distance + reaction_distance
        
        return max(calculated_distance, geometric.stopping_sight_distance)
    
    def get_vehicle_requirements(self, vehicle_type: VehicleType) -> Dict[str, float]:
        """获取车辆设计要求"""
        vehicle_requirements = {
            VehicleType.HEAVY_TRUCK: {
                "min_lane_width": 7.0,
                "min_turning_radius": 15.0,
                "max_grade": 8.0,
                "min_vertical_clearance": 5.0,
                "load_capacity": 150.0  # 吨
            },
            VehicleType.MEDIUM_TRUCK: {
                "min_lane_width": 6.0,
                "min_turning_radius": 12.0,
                "max_grade": 10.0,
                "min_vertical_clearance": 4.5,
                "load_capacity": 75.0
            },
            VehicleType.LIGHT_TRUCK: {
                "min_lane_width": 4.5,
                "min_turning_radius": 8.0,
                "max_grade": 12.0,
                "min_vertical_clearance": 4.0,
                "load_capacity": 25.0
            },
            VehicleType.EXCAVATOR: {
                "min_lane_width": 5.0,
                "min_turning_radius": 6.0,
                "max_grade": 15.0,
                "min_vertical_clearance": 4.5,
                "load_capacity": 0.0
            }
        }
        
        return vehicle_requirements.get(vehicle_type, {})
    
    def recommend_road_class(self, traffic_volume: float, vehicle_type: VehicleType,
                           terrain_difficulty: str = "medium") -> RoadClass:
        """推荐道路等级"""
        # 基于交通量和车辆类型推荐道路等级
        if traffic_volume > 100:  # 车次/天
            if vehicle_type == VehicleType.HEAVY_TRUCK:
                return RoadClass.MAIN_HAUL
            else:
                return RoadClass.SECONDARY_HAUL
        elif traffic_volume > 50:
            return RoadClass.SECONDARY_HAUL
        else:
            return RoadClass.ACCESS_ROAD
    
    def export_standards_to_json(self, file_path: str):
        """导出标准到JSON文件"""
        # 创建可序列化的字典
        export_data = {}
        
        for road_class, standards in self.standards.items():
            export_data[road_class] = {}
            
            for category, standard_obj in standards.items():
                if hasattr(standard_obj, '__dict__'):
                    export_data[road_class][category] = standard_obj.__dict__
                else:
                    export_data[road_class][category] = standard_obj
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)


class DesignStandardsValidator:
    """设计标准验证器"""
    
    def __init__(self, standards: MiningRoadStandards):
        self.standards = standards
    
    def validate_horizontal_alignment(self, road_class: RoadClass, 
                                    alignment_data: List[Dict[str, Any]]) -> List[str]:
        """验证水平线形"""
        errors = []
        geometric = self.standards.get_geometric_standards(road_class)
        
        if not geometric:
            return ["未找到设计标准"]
        
        for i, element in enumerate(alignment_data):
            if element.get("type") == "circular":
                radius = element.get("radius", 0)
                if radius < geometric.min_horizontal_radius:
                    errors.append(f"元素{i}: 圆曲线半径{radius}m小于标准{geometric.min_horizontal_radius}m")
                
                length = element.get("length", 0)
                if length < geometric.min_curve_length:
                    errors.append(f"元素{i}: 曲线长度{length}m小于标准{geometric.min_curve_length}m")
        
        return errors
    
    def validate_vertical_alignment(self, road_class: RoadClass,
                                  vertical_data: List[Dict[str, Any]]) -> List[str]:
        """验证竖直线形"""
        errors = []
        geometric = self.standards.get_geometric_standards(road_class)
        safety = self.standards.get_safety_standards(road_class)
        
        if not geometric or not safety:
            return ["未找到设计标准"]
        
        continuous_grade_length = 0.0
        
        for i, element in enumerate(vertical_data):
            if element.get("type") == "grade":
                grade = abs(element.get("grade", 0))
                length = element.get("length", 0)
                
                # 检查坡度限制
                if grade > geometric.max_grade:
                    errors.append(f"元素{i}: 纵坡{grade}%超过最大值{geometric.max_grade}%")
                
                if grade < geometric.min_grade:
                    errors.append(f"元素{i}: 纵坡{grade}%小于最小值{geometric.min_grade}%")
                
                # 累计连续坡长
                if grade > 3.0:  # 大于3%的坡度
                    continuous_grade_length += length
                else:
                    continuous_grade_length = 0.0
                
                # 检查连续坡长
                if continuous_grade_length > safety.max_continuous_grade_length:
                    errors.append(f"元素{i}: 连续坡长{continuous_grade_length}m超过限制{safety.max_continuous_grade_length}m")
            
            elif element.get("type") == "vertical_curve":
                radius = element.get("radius", 0)
                curve_type = element.get("curve_type", "crest")
                
                min_radius = (geometric.min_vertical_radius_crest if curve_type == "crest" 
                            else geometric.min_vertical_radius_sag)
                
                if radius < min_radius:
                    errors.append(f"元素{i}: 竖曲线半径{radius}m小于标准{min_radius}m")
        
        return errors
    
    def validate_cross_section(self, road_class: RoadClass,
                             cross_section_data: Dict[str, Any]) -> List[str]:
        """验证横断面"""
        errors = []
        geometric = self.standards.get_geometric_standards(road_class)
        safety = self.standards.get_safety_standards(road_class)
        
        if not geometric or not safety:
            return ["未找到设计标准"]
        
        # 检查车道宽度
        lane_width = cross_section_data.get("lane_width", 0)
        if lane_width < geometric.lane_width:
            errors.append(f"车道宽度{lane_width}m小于标准{geometric.lane_width}m")
        
        # 检查路肩宽度
        shoulder_width = cross_section_data.get("shoulder_width", 0)
        if shoulder_width < geometric.shoulder_width:
            errors.append(f"路肩宽度{shoulder_width}m小于标准{geometric.shoulder_width}m")
        
        # 检查边坡坡率
        cut_slope = cross_section_data.get("cut_slope_ratio", 0)
        if cut_slope < geometric.cut_slope_ratio:
            errors.append(f"挖方边坡坡率1:{cut_slope}缓于标准1:{geometric.cut_slope_ratio}")
        
        fill_slope = cross_section_data.get("fill_slope_ratio", 0)
        if fill_slope < geometric.fill_slope_ratio:
            errors.append(f"填方边坡坡率1:{fill_slope}缓于标准1:{geometric.fill_slope_ratio}")
        
        return errors


# 工厂函数
def create_mining_road_standards() -> MiningRoadStandards:
    """创建露天矿山道路设计标准"""
    return MiningRoadStandards()


def create_standards_validator(standards: Optional[MiningRoadStandards] = None) -> DesignStandardsValidator:
    """创建设计标准验证器"""
    if standards is None:
        standards = create_mining_road_standards()
    return DesignStandardsValidator(standards)


def get_recommended_standards(traffic_volume: float, vehicle_type: VehicleType) -> Tuple[RoadClass, GeometricStandards]:
    """获取推荐的设计标准"""
    standards = create_mining_road_standards()
    road_class = standards.recommend_road_class(traffic_volume, vehicle_type)
    geometric = standards.get_geometric_standards(road_class)
    
    return road_class, geometric