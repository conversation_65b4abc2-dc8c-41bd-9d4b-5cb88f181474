"""
设计标准模块测试
Design Standards Module Tests
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.design_standards import (
    MiningRoadStandards, DesignStandardsValidator,
    RoadClass, VehicleType, GeometricStandards, SafetyStandards,
    create_mining_road_standards, create_standards_validator,
    get_recommended_standards
)


def test_mining_road_standards():
    """测试露天矿山道路标准"""
    print("🔍 测试露天矿山道路标准...")
    
    standards = create_mining_road_standards()
    
    # 测试主运输道路标准
    main_haul_geometric = standards.get_geometric_standards(RoadClass.MAIN_HAUL)
    assert main_haul_geometric is not None, "未找到主运输道路几何标准"
    assert main_haul_geometric.design_speed == 40.0, "设计速度错误"
    assert main_haul_geometric.lane_width == 7.0, "车道宽度错误"
    assert main_haul_geometric.max_grade == 8.0, "最大坡度错误"
    
    print(f"✅ 主运输道路标准: 设计速度{main_haul_geometric.design_speed}km/h, 车道宽度{main_haul_geometric.lane_width}m")
    
    # 测试坡道标准
    ramp_geometric = standards.get_geometric_standards(RoadClass.RAMP_ROAD)
    assert ramp_geometric is not None, "未找到坡道几何标准"
    assert ramp_geometric.max_grade == 12.0, "坡道最大坡度错误"
    assert ramp_geometric.lane_width == 8.0, "坡道车道宽度错误"
    
    print(f"✅ 坡道标准: 最大坡度{ramp_geometric.max_grade}%, 车道宽度{ramp_geometric.lane_width}m")


def test_safety_standards():
    """测试安全标准"""
    print("\n🔍 测试安全标准...")
    
    standards = create_mining_road_standards()
    
    # 测试主运输道路安全标准
    main_haul_safety = standards.get_safety_standards(RoadClass.MAIN_HAUL)
    assert main_haul_safety is not None, "未找到主运输道路安全标准"
    assert main_haul_safety.barrier_height == 1.2, "护栏高度错误"
    assert main_haul_safety.passing_bay_spacing == 500.0, "会车道间距错误"
    
    print(f"✅ 主运输道路安全标准: 护栏高度{main_haul_safety.barrier_height}m, 会车道间距{main_haul_safety.passing_bay_spacing}m")
    
    # 测试坡道安全标准
    ramp_safety = standards.get_safety_standards(RoadClass.RAMP_ROAD)
    assert ramp_safety is not None, "未找到坡道安全标准"
    assert ramp_safety.barrier_height == 1.5, "坡道护栏高度错误"
    assert ramp_safety.max_continuous_grade_length == 150.0, "坡道连续坡长限制错误"
    
    print(f"✅ 坡道安全标准: 护栏高度{ramp_safety.barrier_height}m, 最大连续坡长{ramp_safety.max_continuous_grade_length}m")


def test_parameter_validation():
    """测试参数验证"""
    print("\n🔍 测试参数验证...")
    
    standards = create_mining_road_standards()
    
    # 测试有效参数
    valid_params = {
        "horizontal_radius": 80.0,
        "grade": 6.0,
        "superelevation": 4.0,
        "vertical_radius": 500.0
    }
    
    errors = standards.validate_design_parameters(RoadClass.MAIN_HAUL, valid_params)
    assert len(errors) == 0, f"有效参数验证失败: {errors}"
    print("✅ 有效参数验证通过")
    
    # 测试无效参数
    invalid_params = {
        "horizontal_radius": 30.0,  # 小于最小值60m
        "grade": 12.0,  # 超过最大值8%
        "superelevation": 8.0,  # 超过最大值6%
        "vertical_radius": 200.0  # 小于最小值400m
    }
    
    errors = standards.validate_design_parameters(RoadClass.MAIN_HAUL, invalid_params)
    assert len(errors) > 0, "无效参数验证失败"
    print(f"✅ 无效参数验证通过: 发现{len(errors)}个错误")
    
    for error in errors:
        print(f"   ⚠️  {error}")


def test_minimum_radius_calculation():
    """测试最小半径计算"""
    print("\n🔍 测试最小半径计算...")
    
    standards = create_mining_road_standards()
    
    # 测试主运输道路最小半径
    min_radius = standards.calculate_minimum_radius(RoadClass.MAIN_HAUL)
    assert min_radius >= 60.0, f"最小半径计算错误: {min_radius}"
    print(f"✅ 主运输道路最小半径: {min_radius:.1f}m")
    
    # 测试不同设计速度的最小半径
    min_radius_50 = standards.calculate_minimum_radius(RoadClass.MAIN_HAUL, 50.0)
    assert min_radius_50 > min_radius, "高速度最小半径应该更大"
    print(f"✅ 50km/h设计速度最小半径: {min_radius_50:.1f}m")


def test_stopping_sight_distance():
    """测试停车视距计算"""
    print("\n🔍 测试停车视距计算...")
    
    standards = create_mining_road_standards()
    
    # 测试主运输道路停车视距
    sight_distance = standards.calculate_stopping_sight_distance(RoadClass.MAIN_HAUL)
    assert sight_distance >= 40.0, f"停车视距计算错误: {sight_distance}"
    print(f"✅ 主运输道路停车视距: {sight_distance:.1f}m")
    
    # 测试不同设计速度的停车视距
    sight_distance_50 = standards.calculate_stopping_sight_distance(RoadClass.MAIN_HAUL, 50.0)
    assert sight_distance_50 > sight_distance, "高速度停车视距应该更大"
    print(f"✅ 50km/h设计速度停车视距: {sight_distance_50:.1f}m")


def test_vehicle_requirements():
    """测试车辆要求"""
    print("\n🔍 测试车辆要求...")
    
    standards = create_mining_road_standards()
    
    # 测试重型卡车要求
    heavy_truck_req = standards.get_vehicle_requirements(VehicleType.HEAVY_TRUCK)
    assert heavy_truck_req["min_lane_width"] == 7.0, "重型卡车车道宽度要求错误"
    assert heavy_truck_req["max_grade"] == 8.0, "重型卡车最大坡度要求错误"
    print(f"✅ 重型卡车要求: 最小车道宽度{heavy_truck_req['min_lane_width']}m, 最大坡度{heavy_truck_req['max_grade']}%")
    
    # 测试轻型卡车要求
    light_truck_req = standards.get_vehicle_requirements(VehicleType.LIGHT_TRUCK)
    assert light_truck_req["min_lane_width"] == 4.5, "轻型卡车车道宽度要求错误"
    assert light_truck_req["max_grade"] == 12.0, "轻型卡车最大坡度要求错误"
    print(f"✅ 轻型卡车要求: 最小车道宽度{light_truck_req['min_lane_width']}m, 最大坡度{light_truck_req['max_grade']}%")


def test_road_class_recommendation():
    """测试道路等级推荐"""
    print("\n🔍 测试道路等级推荐...")
    
    standards = create_mining_road_standards()
    
    # 测试高交通量重型车辆
    road_class = standards.recommend_road_class(150, VehicleType.HEAVY_TRUCK)
    assert road_class == RoadClass.MAIN_HAUL, f"高交通量重型车辆推荐错误: {road_class}"
    print(f"✅ 高交通量重型车辆推荐: {road_class}")
    
    # 测试中等交通量中型车辆
    road_class = standards.recommend_road_class(75, VehicleType.MEDIUM_TRUCK)
    assert road_class == RoadClass.SECONDARY_HAUL, f"中等交通量中型车辆推荐错误: {road_class}"
    print(f"✅ 中等交通量中型车辆推荐: {road_class}")
    
    # 测试低交通量轻型车辆
    road_class = standards.recommend_road_class(25, VehicleType.LIGHT_TRUCK)
    assert road_class == RoadClass.ACCESS_ROAD, f"低交通量轻型车辆推荐错误: {road_class}"
    print(f"✅ 低交通量轻型车辆推荐: {road_class}")


def test_standards_validator():
    """测试标准验证器"""
    print("\n🔍 测试标准验证器...")
    
    validator = create_standards_validator()
    
    # 测试水平线形验证
    horizontal_data = [
        {
            "type": "circular",
            "radius": 45.0,  # 小于主运输道路最小值60m
            "length": 20.0   # 小于最小曲线长度30m
        }
    ]
    
    errors = validator.validate_horizontal_alignment(RoadClass.MAIN_HAUL, horizontal_data)
    assert len(errors) >= 2, f"水平线形验证错误数量不足: {len(errors)}"
    print(f"✅ 水平线形验证: 发现{len(errors)}个错误")
    
    # 测试竖直线形验证
    vertical_data = [
        {
            "type": "grade",
            "grade": 10.0,  # 超过主运输道路最大值8%
            "length": 200.0
        },
        {
            "type": "grade", 
            "grade": 6.0,
            "length": 200.0  # 连续坡长400m超过限制300m
        }
    ]
    
    errors = validator.validate_vertical_alignment(RoadClass.MAIN_HAUL, vertical_data)
    assert len(errors) >= 1, f"竖直线形验证错误数量不足: {len(errors)}"
    print(f"✅ 竖直线形验证: 发现{len(errors)}个错误")
    
    # 测试横断面验证
    cross_section_data = {
        "lane_width": 5.0,  # 小于标准7m
        "shoulder_width": 0.5,  # 小于标准1.5m
        "cut_slope_ratio": 1.0,  # 小于标准1.5
        "fill_slope_ratio": 1.0   # 小于标准1.5
    }
    
    errors = validator.validate_cross_section(RoadClass.MAIN_HAUL, cross_section_data)
    assert len(errors) >= 4, f"横断面验证错误数量不足: {len(errors)}"
    print(f"✅ 横断面验证: 发现{len(errors)}个错误")


def test_recommended_standards():
    """测试推荐标准获取"""
    print("\n🔍 测试推荐标准获取...")
    
    road_class, geometric = get_recommended_standards(120, VehicleType.HEAVY_TRUCK)
    
    assert road_class == RoadClass.MAIN_HAUL, f"推荐道路等级错误: {road_class}"
    assert geometric is not None, "未获取到几何标准"
    assert geometric.design_speed == 40.0, "推荐几何标准错误"
    
    print(f"✅ 推荐标准获取成功: {road_class}, 设计速度{geometric.design_speed}km/h")


def run_all_tests():
    """运行所有测试"""
    print("=" * 70)
    print("设计标准模块测试")
    print("Design Standards Module Tests")
    print("=" * 70)
    
    try:
        test_mining_road_standards()
        test_safety_standards()
        test_parameter_validation()
        test_minimum_radius_calculation()
        test_stopping_sight_distance()
        test_vehicle_requirements()
        test_road_class_recommendation()
        test_standards_validator()
        test_recommended_standards()
        
        print("\n" + "=" * 70)
        print("🎉 所有设计标准测试通过！")
        print("✅ 设计标准模块功能正常")
        print("=" * 70)
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        print("=" * 70)
        return False


if __name__ == "__main__":
    run_all_tests()