"""
道路几何计算核心模块
Road Geometry Calculation Core Module
"""

import math
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass

# 尝试导入numpy，如果不可用则使用标准库
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False


@dataclass
class Point2D:
    """二维点"""
    x: float
    y: float
    
    def distance_to(self, other: 'Point2D') -> float:
        """计算到另一点的距离"""
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)
    
    def angle_to(self, other: 'Point2D') -> float:
        """计算到另一点的方位角（弧度）"""
        return math.atan2(other.y - self.y, other.x - self.x)
    
    def translate(self, dx: float, dy: float) -> 'Point2D':
        """平移"""
        return Point2D(self.x + dx, self.y + dy)
    
    def rotate(self, angle: float, center: Optional['Point2D'] = None) -> 'Point2D':
        """绕点旋转"""
        if center is None:
            center = Point2D(0, 0)
        
        # 平移到原点
        x = self.x - center.x
        y = self.y - center.y
        
        # 旋转
        cos_a = math.cos(angle)
        sin_a = math.sin(angle)
        new_x = x * cos_a - y * sin_a
        new_y = x * sin_a + y * cos_a
        
        # 平移回去
        return Point2D(new_x + center.x, new_y + center.y)


@dataclass
class Point3D:
    """三维点"""
    x: float
    y: float
    z: float
    
    def distance_to(self, other: 'Point3D') -> float:
        """计算到另一点的距离"""
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2 + (self.z - other.z)**2)
    
    def to_2d(self) -> Point2D:
        """转换为二维点"""
        return Point2D(self.x, self.y)


class GeometryCalculator:
    """几何计算器"""
    
    @staticmethod
    def normalize_angle(angle: float) -> float:
        """标准化角度到[0, 2π)"""
        while angle < 0:
            angle += 2 * math.pi
        while angle >= 2 * math.pi:
            angle -= 2 * math.pi
        return angle
    
    @staticmethod
    def degrees_to_radians(degrees: float) -> float:
        """角度转弧度"""
        return degrees * math.pi / 180.0
    
    @staticmethod
    def radians_to_degrees(radians: float) -> float:
        """弧度转角度"""
        return radians * 180.0 / math.pi
    
    @staticmethod
    def calculate_bearing(p1: Point2D, p2: Point2D) -> float:
        """计算方位角（度）"""
        angle = math.atan2(p2.x - p1.x, p2.y - p1.y)
        bearing = GeometryCalculator.radians_to_degrees(angle)
        return GeometryCalculator.normalize_angle(GeometryCalculator.degrees_to_radians(bearing))
    
    @staticmethod
    def point_on_line(start: Point2D, end: Point2D, distance: float) -> Point2D:
        """在直线上按距离计算点"""
        total_distance = start.distance_to(end)
        if total_distance == 0:
            return start
        
        ratio = distance / total_distance
        x = start.x + ratio * (end.x - start.x)
        y = start.y + ratio * (end.y - start.y)
        return Point2D(x, y)
    
    @staticmethod
    def perpendicular_distance(point: Point2D, line_start: Point2D, line_end: Point2D) -> float:
        """计算点到直线的垂直距离"""
        # 使用点到直线距离公式
        A = line_end.y - line_start.y
        B = line_start.x - line_end.x
        C = line_end.x * line_start.y - line_start.x * line_end.y
        
        distance = abs(A * point.x + B * point.y + C) / math.sqrt(A**2 + B**2)
        return distance


class StraightLineCalculator:
    """直线计算器"""
    
    @staticmethod
    def calculate_points(start: Point2D, end: Point2D, interval: float = 20.0) -> List[Point2D]:
        """计算直线上的点"""
        points = [start]
        
        total_length = start.distance_to(end)
        if total_length <= interval:
            points.append(end)
            return points
        
        num_intervals = int(total_length / interval)
        
        for i in range(1, num_intervals + 1):
            distance = i * interval
            point = GeometryCalculator.point_on_line(start, end, distance)
            points.append(point)
        
        # 添加终点
        if points[-1].distance_to(end) > 0.001:  # 避免重复点
            points.append(end)
        
        return points
    
    @staticmethod
    def calculate_length(start: Point2D, end: Point2D) -> float:
        """计算直线长度"""
        return start.distance_to(end)
    
    @staticmethod
    def calculate_azimuth(start: Point2D, end: Point2D) -> float:
        """计算直线方位角"""
        return start.angle_to(end)


class CircularCurveCalculator:
    """圆曲线计算器"""
    
    @staticmethod
    def calculate_center(start: Point2D, end: Point2D, radius: float, 
                        clockwise: bool = True) -> Point2D:
        """计算圆心"""
        # 计算弦长和弦中点
        chord_length = start.distance_to(end)
        mid_point = Point2D((start.x + end.x) / 2, (start.y + end.y) / 2)
        
        # 计算弦心距
        if chord_length > 2 * radius:
            raise ValueError(f"半径{radius}太小，无法连接两点")
        
        chord_center_distance = math.sqrt(radius**2 - (chord_length / 2)**2)
        
        # 计算垂直于弦的方向
        chord_angle = start.angle_to(end)
        perpendicular_angle = chord_angle + (math.pi / 2 if clockwise else -math.pi / 2)
        
        # 计算圆心
        center_x = mid_point.x + chord_center_distance * math.cos(perpendicular_angle)
        center_y = mid_point.y + chord_center_distance * math.sin(perpendicular_angle)
        
        return Point2D(center_x, center_y)
    
    @staticmethod
    def calculate_arc_length(radius: float, central_angle: float) -> float:
        """计算弧长"""
        return radius * abs(central_angle)
    
    @staticmethod
    def calculate_points(center: Point2D, radius: float, start_angle: float, 
                        end_angle: float, interval: float = 20.0) -> List[Point2D]:
        """计算圆弧上的点"""
        points = []
        
        # 计算弧长和角度间隔
        angle_diff = end_angle - start_angle
        arc_length = abs(angle_diff * radius)
        
        if arc_length <= interval:
            # 弧长很短，只返回起点和终点
            start_point = Point2D(
                center.x + radius * math.cos(start_angle),
                center.y + radius * math.sin(start_angle)
            )
            end_point = Point2D(
                center.x + radius * math.cos(end_angle),
                center.y + radius * math.sin(end_angle)
            )
            return [start_point, end_point]
        
        # 计算角度间隔
        angle_interval = interval / radius
        if angle_diff < 0:
            angle_interval = -angle_interval
        
        current_angle = start_angle
        while abs(current_angle - start_angle) < abs(angle_diff):
            x = center.x + radius * math.cos(current_angle)
            y = center.y + radius * math.sin(current_angle)
            points.append(Point2D(x, y))
            current_angle += angle_interval
        
        # 添加终点
        end_x = center.x + radius * math.cos(end_angle)
        end_y = center.y + radius * math.sin(end_angle)
        points.append(Point2D(end_x, end_y))
        
        return points
    
    @staticmethod
    def calculate_tangent_length(radius: float, deflection_angle: float) -> float:
        """计算切线长"""
        return radius * math.tan(abs(deflection_angle) / 2)


class SpiralCurveCalculator:
    """缓和曲线计算器"""
    
    @staticmethod
    def calculate_spiral_coordinates(length: float, parameter_a: float, 
                                   num_points: int = 50) -> List[Tuple[float, float]]:
        """计算缓和曲线坐标"""
        points = []
        
        for i in range(num_points + 1):
            s = length * i / num_points  # 弧长参数
            
            if s == 0:
                points.append((0.0, 0.0))
                continue
            
            # 缓和曲线参数方程
            # 使用级数展开近似计算
            x = s - s**5 / (40 * parameter_a**4) + s**9 / (3456 * parameter_a**8)
            y = s**3 / (6 * parameter_a**2) - s**7 / (336 * parameter_a**6)
            
            points.append((x, y))
        
        return points
    
    @staticmethod
    def calculate_spiral_angle(length: float, parameter_a: float) -> float:
        """计算缓和曲线转角"""
        return length**2 / (2 * parameter_a**2)
    
    @staticmethod
    def calculate_spiral_radius(length: float, parameter_a: float) -> float:
        """计算缓和曲线末端半径"""
        if length == 0:
            return float('inf')
        return parameter_a**2 / length


class HorizontalAlignmentCalculator:
    """水平线形计算器"""
    
    def __init__(self):
        self.straight_calc = StraightLineCalculator()
        self.circular_calc = CircularCurveCalculator()
        self.spiral_calc = SpiralCurveCalculator()
    
    def calculate_alignment_points(self, elements: List[Dict[str, Any]], 
                                 interval: float = 20.0) -> List[Point2D]:
        """计算整条线形的点"""
        all_points = []
        
        for element in elements:
            element_type = element.get('type')
            
            if element_type == 'straight':
                start = Point2D(element['start_x'], element['start_y'])
                end = Point2D(element['end_x'], element['end_y'])
                points = self.straight_calc.calculate_points(start, end, interval)
                
            elif element_type == 'circular':
                center = Point2D(element['center_x'], element['center_y'])
                radius = element['radius']
                start_angle = element['start_angle']
                end_angle = element['end_angle']
                points = self.circular_calc.calculate_points(
                    center, radius, start_angle, end_angle, interval
                )
                
            elif element_type == 'spiral':
                # 缓和曲线计算较复杂，这里简化处理
                length = element['length']
                parameter_a = element['parameter_a']
                coords = self.spiral_calc.calculate_spiral_coordinates(length, parameter_a)
                points = [Point2D(x, y) for x, y in coords]
                
            else:
                continue
            
            # 避免重复点
            if all_points and points:
                if all_points[-1].distance_to(points[0]) < 0.001:
                    points = points[1:]
            
            all_points.extend(points)
        
        return all_points
    
    def validate_alignment_continuity(self, elements: List[Dict[str, Any]]) -> List[str]:
        """验证线形连续性"""
        errors = []
        
        for i in range(1, len(elements)):
            prev_element = elements[i-1]
            curr_element = elements[i]
            
            # 检查终点和起点是否连接
            prev_end = Point2D(prev_element.get('end_x', 0), prev_element.get('end_y', 0))
            curr_start = Point2D(curr_element.get('start_x', 0), curr_element.get('start_y', 0))
            
            distance = prev_end.distance_to(curr_start)
            if distance > 0.001:  # 1mm容差
                errors.append(f"元素{i-1}和{i}之间不连续，间距{distance:.3f}m")
        
        return errors


class VerticalAlignmentCalculator:
    """竖直线形计算器"""
    
    @staticmethod
    def calculate_grade_elevation(start_station: float, start_elevation: float,
                                end_station: float, end_elevation: float,
                                station: float) -> float:
        """计算坡段高程"""
        if station < start_station or station > end_station:
            raise ValueError("桩号超出坡段范围")
        
        length = end_station - start_station
        if length == 0:
            return start_elevation
        
        grade = (end_elevation - start_elevation) / length
        elevation = start_elevation + grade * (station - start_station)
        return elevation
    
    @staticmethod
    def calculate_vertical_curve_elevation(pvi_station: float, pvi_elevation: float,
                                         grade1: float, grade2: float, radius: float,
                                         station: float) -> float:
        """计算竖曲线高程"""
        # 计算竖曲线长度
        grade_diff = abs(grade2 - grade1) / 100.0  # 转换为小数
        curve_length = radius * grade_diff
        
        # 计算竖曲线起终点桩号
        start_station = pvi_station - curve_length / 2
        end_station = pvi_station + curve_length / 2
        
        if station < start_station or station > end_station:
            # 在竖曲线外，按直坡计算
            if station < start_station:
                return pvi_elevation + (grade1 / 100.0) * (station - pvi_station)
            else:
                return pvi_elevation + (grade2 / 100.0) * (station - pvi_station)
        
        # 在竖曲线内
        x = station - pvi_station
        y = pvi_elevation + (grade1 / 100.0) * x + (grade_diff / (2 * curve_length)) * x**2
        
        return y
    
    @staticmethod
    def calculate_grade_percentage(start_elevation: float, end_elevation: float,
                                 horizontal_distance: float) -> float:
        """计算坡度百分比"""
        if horizontal_distance == 0:
            return 0.0
        
        return ((end_elevation - start_elevation) / horizontal_distance) * 100.0
    
    @staticmethod
    def validate_grade_limits(grade: float, max_grade: float = 8.0) -> bool:
        """验证坡度是否符合限制"""
        return abs(grade) <= max_grade


# 工厂函数
def create_geometry_calculator() -> GeometryCalculator:
    """创建几何计算器"""
    return GeometryCalculator()


def create_horizontal_alignment_calculator() -> HorizontalAlignmentCalculator:
    """创建水平线形计算器"""
    return HorizontalAlignmentCalculator()


def create_vertical_alignment_calculator() -> VerticalAlignmentCalculator:
    """创建竖直线形计算器"""
    return VerticalAlignmentCalculator()


# 示例使用
def create_sample_straight_line() -> List[Point2D]:
    """创建示例直线"""
    start = Point2D(0, 0)
    end = Point2D(100, 50)
    return StraightLineCalculator.calculate_points(start, end, 20.0)


def create_sample_circular_curve() -> List[Point2D]:
    """创建示例圆曲线"""
    center = Point2D(50, 50)
    radius = 50.0
    start_angle = 0.0
    end_angle = math.pi / 2  # 90度
    return CircularCurveCalculator.calculate_points(center, radius, start_angle, end_angle, 10.0)