"""
简化版应用 - 不依赖外部包的基础版本
Simple Application - Basic version without external dependencies
"""

import json
import os
import sys
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import webbrowser
import time

# 添加项目路径
sys.path.insert(0, os.getcwd())

class MiningRoadHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/':
            self.send_homepage()
        elif path == '/api/info':
            self.send_api_info()
        elif path == '/api/health':
            self.send_health_check()
        elif path == '/docs':
            self.send_docs()
        else:
            self.send_404()
    
    def send_homepage(self):
        """发送主页"""
        html = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>露天矿山道路设计软件</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1 { color: #2c3e50; text-align: center; }
                .status { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
                .api-links { display: flex; gap: 10px; justify-content: center; margin: 20px 0; }
                .api-links a { padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; }
                .api-links a:hover { background: #2980b9; }
                .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
                .feature { background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 4px solid #3498db; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🛣️ 露天矿山道路设计软件</h1>
                <div class="status">
                    <h3>✅ 系统状态: 运行中</h3>
                    <p>版本: 1.0.0 | 模式: 开发环境</p>
                </div>
                
                <div class="api-links">
                    <a href="/api/info">API信息</a>
                    <a href="/api/health">健康检查</a>
                    <a href="/docs">API文档</a>
                </div>
                
                <div class="features">
                    <div class="feature">
                        <h4>🛣️ 道路选线功能</h4>
                        <p>智能道路选线和设计</p>
                    </div>
                    <div class="feature">
                        <h4>🔍 冲突检测功能</h4>
                        <p>自动检测设计冲突</p>
                    </div>
                    <div class="feature">
                        <h4>🛡️ 安全检测功能</h4>
                        <p>安全参数验证和分析</p>
                    </div>
                    <div class="feature">
                        <h4>📏 道路剖切功能</h4>
                        <p>详细的断面分析</p>
                    </div>
                    <div class="feature">
                        <h4>🚛 运输路线优化</h4>
                        <p>多目标路线优化</p>
                    </div>
                    <div class="feature">
                        <h4>🌍 三维GIS可视化</h4>
                        <p>基于Cesium的三维展示</p>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px; color: #7f8c8d;">
                    <p>Mining Road Design Software - 专业的露天矿山道路设计与优化系统</p>
                </div>
            </div>
        </body>
        </html>
        """
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_api_info(self):
        """发送API信息"""
        data = {
            "name": "露天矿山道路设计软件",
            "version": "1.0.0",
            "description": "Mining Road Design Software",
            "api_version": "v1",
            "status": "running",
            "endpoints": {
                "root": "/ - 系统主页",
                "health": "/api/health - 健康检查",
                "info": "/api/info - API信息",
                "docs": "/docs - API文档"
            }
        }
        self.send_json_response(data)
    
    def send_health_check(self):
        """发送健康检查"""
        data = {
            "status": "healthy",
            "timestamp": time.time(),
            "services": {
                "web_server": "running",
                "database": "not_configured",
                "redis": "not_configured"
            }
        }
        self.send_json_response(data)
    
    def send_docs(self):
        """发送API文档"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>API文档 - 露天矿山道路设计软件</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .endpoint { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .method { background: #28a745; color: white; padding: 5px 10px; border-radius: 3px; font-size: 12px; }
            </style>
        </head>
        <body>
            <h1>API文档</h1>
            <div class="endpoint">
                <span class="method">GET</span> <strong>/</strong> - 系统主页
            </div>
            <div class="endpoint">
                <span class="method">GET</span> <strong>/api/info</strong> - API信息
            </div>
            <div class="endpoint">
                <span class="method">GET</span> <strong>/api/health</strong> - 健康检查
            </div>
            <div class="endpoint">
                <span class="method">GET</span> <strong>/docs</strong> - API文档
            </div>
        </body>
        </html>
        """
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def send_404(self):
        """发送404响应"""
        self.send_response(404)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        html = "<h1>404 - 页面未找到</h1>"
        self.wfile.write(html.encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def start_server(host='localhost', port=8000):
    """启动服务器"""
    server_address = (host, port)
    httpd = HTTPServer(server_address, MiningRoadHandler)
    
    print("=" * 60)
    print("露天矿山道路设计软件 - 简化版服务器")
    print("Mining Road Design Software - Simple Server")
    print("=" * 60)
    print(f"🚀 服务器启动成功!")
    print(f"   地址: http://{host}:{port}")
    print(f"   API信息: http://{host}:{port}/api/info")
    print(f"   健康检查: http://{host}:{port}/api/health")
    print(f"   API文档: http://{host}:{port}/docs")
    print("=" * 60)
    print("按 Ctrl+C 停止服务器")
    
    # 自动打开浏览器
    def open_browser():
        time.sleep(1)
        webbrowser.open(f'http://{host}:{port}')
    
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == "__main__":
    start_server()
