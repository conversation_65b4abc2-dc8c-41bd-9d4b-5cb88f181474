-- 露天矿山道路设计软件数据库初始化脚本
-- Mining Road Design Software Database Initialization

-- 启用PostGIS扩展
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;

-- 创建应用用户（如果需要）
-- CREATE USER mining_app WITH PASSWORD 'mining_app_password';
-- GRANT ALL PRIVILEGES ON DATABASE mining_roads TO mining_app;

-- 设置时区
SET timezone = 'UTC';

-- 创建基础表结构将在后续任务中通过SQLAlchemy模型自动创建

-- 插入初始数据
INSERT INTO spatial_ref_sys (srid, auth_name, auth_srid, proj4text, srtext) 
VALUES (4326, 'EPSG', 4326, '+proj=longlat +datum=WGS84 +no_defs', 
'GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]]')
ON CONFLICT (srid) DO NOTHING;

-- 创建索引优化查询性能
-- 具体索引将在模型创建后添加

COMMIT;