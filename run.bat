@echo off
echo 启动露天矿山道路设计软件...
echo Starting Mining Road Design Software...

REM 检查Python环境
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境
    echo Error: Python not found
    pause
    exit /b 1
)

REM 安装依赖
echo 安装依赖包...
echo Installing dependencies...
pip install -r requirements.txt

REM 设置环境变量
set PYTHONPATH=%cd%

REM 启动应用
echo 启动应用服务器...
echo Starting application server...
python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

pause