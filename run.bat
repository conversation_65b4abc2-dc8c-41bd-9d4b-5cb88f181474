@echo off
echo ============================================================
echo 露天矿山道路设计软件 - 开发环境启动脚本
echo Mining Road Design Software - Development Startup Script
echo ============================================================

REM 检查Python环境
echo 🔍 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python环境
    echo    请安装Python 3.8+并添加到PATH环境变量
    pause
    exit /b 1
)

REM 检查环境配置文件
if not exist ".env" (
    echo 🔧 创建环境配置文件...
    copy ".env.example" ".env"
    echo ✅ 已创建 .env 文件
)

REM 安装开发依赖
echo 📦 安装开发依赖包...
pip install -r requirements_dev.txt
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，尝试安装基础依赖...
    pip install fastapi uvicorn sqlalchemy pydantic python-dotenv
)

REM 设置环境变量
set PYTHONPATH=%cd%

REM 创建必要目录
if not exist "uploads" mkdir uploads
if not exist "logs" mkdir logs
if not exist "static" mkdir static

REM 启动应用
echo 🚀 启动应用服务器...
echo    访问地址: http://localhost:8000
echo    API文档: http://localhost:8000/docs
echo ============================================================
python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

pause