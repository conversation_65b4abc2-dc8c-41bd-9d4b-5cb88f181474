"""
CAD数据处理服务
CAD Data Processing Service
"""

import os
import json
import math
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from pathlib import Path
from enum import Enum

# 尝试导入ezdxf库
try:
    import ezdxf
    from ezdxf import recover
    from ezdxf.entities import Line, Circle, Arc, Polyline, LWPolyline, Point, Text, Insert
    EZDXF_AVAILABLE = True
except ImportError:
    EZDXF_AVAILABLE = False

from src.services.gis_service import TerrainPoint, BoundingBox3D


class CADEntityType(str, Enum):
    """CAD实体类型"""
    LINE = "line"
    CIRCLE = "circle"
    ARC = "arc"
    POLYLINE = "polyline"
    POINT = "point"
    TEXT = "text"
    BLOCK = "block"
    DIMENSION = "dimension"
    HATCH = "hatch"


@dataclass
class CADPoint:
    """CAD点"""
    x: float
    y: float
    z: float = 0.0
    
    def to_terrain_point(self) -> TerrainPoint:
        """转换为地形点"""
        return TerrainPoint(self.x, self.y, self.z)


@dataclass
class CADEntity:
    """CAD实体"""
    entity_type: CADEntityType
    layer: str
    color: int = 256  # 256表示ByLayer
    linetype: str = "BYLAYER"
    geometry: Dict[str, Any] = None
    attributes: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.geometry is None:
            self.geometry = {}
        if self.attributes is None:
            self.attributes = {}


@dataclass
class CADLayer:
    """CAD图层"""
    name: str
    color: int = 7  # 默认白色
    linetype: str = "CONTINUOUS"
    lineweight: float = 0.25
    visible: bool = True
    locked: bool = False
    entities: List[CADEntity] = None
    
    def __post_init__(self):
        if self.entities is None:
            self.entities = []


@dataclass
class CADDrawing:
    """CAD图纸"""
    filename: str
    layers: List[CADLayer]
    bounds: BoundingBox3D
    entity_count: int = 0
    coordinate_system: str = "Local"
    units: str = "Meters"
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ImportResult:
    """导入结果"""
    success: bool
    message: str
    drawing: Optional[CADDrawing] = None
    entity_count: int = 0
    layer_count: int = 0
    errors: List[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


class CADImporter:
    """CAD数据导入器"""
    
    def __init__(self):
        self.supported_formats = {'.dxf': self._import_dxf}
        if EZDXF_AVAILABLE:
            self.supported_formats['.dwg'] = self._import_dwg
    
    def import_cad_file(self, file_path: str) -> ImportResult:
        """导入CAD文件"""
        if not os.path.exists(file_path):
            return ImportResult(False, f"文件不存在: {file_path}")
        
        file_ext = Path(file_path).suffix.lower()
        if file_ext not in self.supported_formats:
            return ImportResult(False, f"不支持的文件格式: {file_ext}")
        
        if not EZDXF_AVAILABLE:
            return ImportResult(False, "需要安装ezdxf库来处理CAD文件")
        
        try:
            import_func = self.supported_formats[file_ext]
            return import_func(file_path)
        except Exception as e:
            return ImportResult(False, f"导入失败: {str(e)}")
    
    def _import_dxf(self, file_path: str) -> ImportResult:
        """导入DXF文件"""
        try:
            # 尝试正常读取，如果失败则使用恢复模式
            try:
                doc = ezdxf.readfile(file_path)
            except ezdxf.DXFStructureError:
                doc, auditor = recover.readfile(file_path)
                if auditor.has_errors:
                    errors = [str(error) for error in auditor.errors]
                else:
                    errors = []
            else:
                errors = []
            
            # 解析图纸
            drawing = self._parse_dxf_document(doc, Path(file_path).name)
            
            return ImportResult(
                success=True,
                message=f"成功导入DXF文件，{drawing.entity_count}个实体",
                drawing=drawing,
                entity_count=drawing.entity_count,
                layer_count=len(drawing.layers),
                errors=errors
            )
            
        except Exception as e:
            return ImportResult(False, f"DXF文件解析失败: {str(e)}")
    
    def _import_dwg(self, file_path: str) -> ImportResult:
        """导入DWG文件（需要特殊处理）"""
        # ezdxf不直接支持DWG，需要转换
        return ImportResult(False, "DWG格式需要额外的转换工具，当前版本暂不支持")
    
    def _parse_dxf_document(self, doc, filename: str) -> CADDrawing:
        """解析DXF文档"""
        layers = []
        entity_count = 0
        
        # 解析图层
        layer_dict = {}
        for layer in doc.layers:
            cad_layer = CADLayer(
                name=layer.dxf.name,
                color=getattr(layer.dxf, 'color', 7),
                linetype=getattr(layer.dxf, 'linetype', 'CONTINUOUS'),
                visible=not getattr(layer.dxf, 'off', False),
                locked=getattr(layer.dxf, 'lock', False)
            )
            layers.append(cad_layer)
            layer_dict[layer.dxf.name] = cad_layer
        
        # 解析实体
        modelspace = doc.modelspace()
        
        min_x = min_y = min_z = float('inf')
        max_x = max_y = max_z = float('-inf')
        
        for entity in modelspace:
            try:
                cad_entity = self._parse_entity(entity)
                if cad_entity:
                    # 添加到对应图层
                    layer_name = getattr(entity.dxf, 'layer', '0')
                    if layer_name in layer_dict:
                        layer_dict[layer_name].entities.append(cad_entity)
                    else:
                        # 创建默认图层
                        if '0' not in layer_dict:
                            default_layer = CADLayer(name='0')
                            layers.append(default_layer)
                            layer_dict['0'] = default_layer
                        layer_dict['0'].entities.append(cad_entity)
                    
                    entity_count += 1
                    
                    # 更新边界
                    bounds = self._get_entity_bounds(cad_entity)
                    if bounds:
                        min_x = min(min_x, bounds[0])
                        min_y = min(min_y, bounds[1])
                        min_z = min(min_z, bounds[2])
                        max_x = max(max_x, bounds[3])
                        max_y = max(max_y, bounds[4])
                        max_z = max(max_z, bounds[5])
                        
            except Exception as e:
                # 跳过无法解析的实体
                continue
        
        # 创建边界框
        if entity_count > 0:
            bounds = BoundingBox3D(min_x, min_y, min_z, max_x, max_y, max_z)
        else:
            bounds = BoundingBox3D(0, 0, 0, 0, 0, 0)
        
        # 获取图纸信息
        units = "Meters"  # 默认单位
        if hasattr(doc.header, '$INSUNITS'):
            units_code = doc.header.get('$INSUNITS', 6)
            units = self._get_units_name(units_code)
        
        return CADDrawing(
            filename=filename,
            layers=layers,
            bounds=bounds,
            entity_count=entity_count,
            units=units,
            metadata={
                'dxf_version': doc.dxfversion,
                'created_by': getattr(doc.header, '$ACADVER', 'Unknown')
            }
        )
    
    def _parse_entity(self, entity) -> Optional[CADEntity]:
        """解析CAD实体"""
        entity_type = entity.dxftype()
        layer = getattr(entity.dxf, 'layer', '0')
        color = getattr(entity.dxf, 'color', 256)
        linetype = getattr(entity.dxf, 'linetype', 'BYLAYER')
        
        geometry = {}
        attributes = {'dxf_type': entity_type}
        
        if entity_type == 'LINE':
            geometry = {
                'start': [entity.dxf.start.x, entity.dxf.start.y, entity.dxf.start.z],
                'end': [entity.dxf.end.x, entity.dxf.end.y, entity.dxf.end.z]
            }
            return CADEntity(CADEntityType.LINE, layer, color, linetype, geometry, attributes)
        
        elif entity_type == 'CIRCLE':
            geometry = {
                'center': [entity.dxf.center.x, entity.dxf.center.y, entity.dxf.center.z],
                'radius': entity.dxf.radius
            }
            return CADEntity(CADEntityType.CIRCLE, layer, color, linetype, geometry, attributes)
        
        elif entity_type == 'ARC':
            geometry = {
                'center': [entity.dxf.center.x, entity.dxf.center.y, entity.dxf.center.z],
                'radius': entity.dxf.radius,
                'start_angle': entity.dxf.start_angle,
                'end_angle': entity.dxf.end_angle
            }
            return CADEntity(CADEntityType.ARC, layer, color, linetype, geometry, attributes)
        
        elif entity_type in ['POLYLINE', 'LWPOLYLINE']:
            points = []
            if hasattr(entity, 'vertices'):
                for vertex in entity.vertices:
                    points.append([vertex.dxf.location.x, vertex.dxf.location.y, vertex.dxf.location.z])
            elif hasattr(entity, 'get_points'):
                for point in entity.get_points():
                    points.append([point[0], point[1], 0.0])
            
            geometry = {
                'points': points,
                'closed': getattr(entity.dxf, 'flags', 0) & 1 == 1
            }
            return CADEntity(CADEntityType.POLYLINE, layer, color, linetype, geometry, attributes)
        
        elif entity_type == 'POINT':
            geometry = {
                'location': [entity.dxf.location.x, entity.dxf.location.y, entity.dxf.location.z]
            }
            return CADEntity(CADEntityType.POINT, layer, color, linetype, geometry, attributes)
        
        elif entity_type == 'TEXT':
            geometry = {
                'location': [entity.dxf.insert.x, entity.dxf.insert.y, entity.dxf.insert.z],
                'text': entity.dxf.text,
                'height': entity.dxf.height,
                'rotation': getattr(entity.dxf, 'rotation', 0.0)
            }
            return CADEntity(CADEntityType.TEXT, layer, color, linetype, geometry, attributes)
        
        elif entity_type == 'INSERT':
            geometry = {
                'location': [entity.dxf.insert.x, entity.dxf.insert.y, entity.dxf.insert.z],
                'block_name': entity.dxf.name,
                'scale': [getattr(entity.dxf, 'xscale', 1.0), 
                         getattr(entity.dxf, 'yscale', 1.0), 
                         getattr(entity.dxf, 'zscale', 1.0)],
                'rotation': getattr(entity.dxf, 'rotation', 0.0)
            }
            return CADEntity(CADEntityType.BLOCK, layer, color, linetype, geometry, attributes)
        
        # 其他实体类型暂时跳过
        return None
    
    def _get_entity_bounds(self, entity: CADEntity) -> Optional[Tuple[float, float, float, float, float, float]]:
        """获取实体边界"""
        geometry = entity.geometry
        
        if entity.entity_type == CADEntityType.LINE:
            start = geometry['start']
            end = geometry['end']
            return (
                min(start[0], end[0]), min(start[1], end[1]), min(start[2], end[2]),
                max(start[0], end[0]), max(start[1], end[1]), max(start[2], end[2])
            )
        
        elif entity.entity_type == CADEntityType.CIRCLE:
            center = geometry['center']
            radius = geometry['radius']
            return (
                center[0] - radius, center[1] - radius, center[2],
                center[0] + radius, center[1] + radius, center[2]
            )
        
        elif entity.entity_type == CADEntityType.ARC:
            center = geometry['center']
            radius = geometry['radius']
            # 简化处理，使用整个圆的边界
            return (
                center[0] - radius, center[1] - radius, center[2],
                center[0] + radius, center[1] + radius, center[2]
            )
        
        elif entity.entity_type == CADEntityType.POLYLINE:
            points = geometry['points']
            if not points:
                return None
            
            xs = [p[0] for p in points]
            ys = [p[1] for p in points]
            zs = [p[2] for p in points]
            
            return (min(xs), min(ys), min(zs), max(xs), max(ys), max(zs))
        
        elif entity.entity_type == CADEntityType.POINT:
            loc = geometry['location']
            return (loc[0], loc[1], loc[2], loc[0], loc[1], loc[2])
        
        elif entity.entity_type in [CADEntityType.TEXT, CADEntityType.BLOCK]:
            loc = geometry['location']
            return (loc[0], loc[1], loc[2], loc[0], loc[1], loc[2])
        
        return None
    
    def _get_units_name(self, units_code: int) -> str:
        """获取单位名称"""
        units_map = {
            0: "Unitless",
            1: "Inches",
            2: "Feet",
            3: "Miles",
            4: "Millimeters",
            5: "Centimeters",
            6: "Meters",
            7: "Kilometers",
            8: "Microinches",
            9: "Mils",
            10: "Yards",
            11: "Angstroms",
            12: "Nanometers",
            13: "Microns",
            14: "Decimeters",
            15: "Decameters",
            16: "Hectometers",
            17: "Gigameters",
            18: "Astronomical Units",
            19: "Light Years",
            20: "Parsecs"
        }
        return units_map.get(units_code, "Unknown")
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式"""
        return list(self.supported_formats.keys())


class CADAnalyzer:
    """CAD数据分析器"""
    
    @staticmethod
    def analyze_drawing(drawing: CADDrawing) -> Dict[str, Any]:
        """分析CAD图纸"""
        analysis = {
            'basic_info': {
                'filename': drawing.filename,
                'entity_count': drawing.entity_count,
                'layer_count': len(drawing.layers),
                'units': drawing.units,
                'coordinate_system': drawing.coordinate_system
            },
            'bounds': {
                'min_x': drawing.bounds.min_x,
                'min_y': drawing.bounds.min_y,
                'min_z': drawing.bounds.min_z,
                'max_x': drawing.bounds.max_x,
                'max_y': drawing.bounds.max_y,
                'max_z': drawing.bounds.max_z,
                'width': drawing.bounds.max_x - drawing.bounds.min_x,
                'height': drawing.bounds.max_y - drawing.bounds.min_y,
                'depth': drawing.bounds.max_z - drawing.bounds.min_z
            },
            'layers': [],
            'entity_types': {},
            'statistics': {}
        }
        
        # 分析图层
        for layer in drawing.layers:
            layer_info = {
                'name': layer.name,
                'entity_count': len(layer.entities),
                'visible': layer.visible,
                'locked': layer.locked,
                'color': layer.color,
                'entity_types': {}
            }
            
            # 统计图层中的实体类型
            for entity in layer.entities:
                entity_type = entity.entity_type
                if entity_type not in layer_info['entity_types']:
                    layer_info['entity_types'][entity_type] = 0
                layer_info['entity_types'][entity_type] += 1
            
            analysis['layers'].append(layer_info)
        
        # 统计所有实体类型
        for layer in drawing.layers:
            for entity in layer.entities:
                entity_type = entity.entity_type
                if entity_type not in analysis['entity_types']:
                    analysis['entity_types'][entity_type] = 0
                analysis['entity_types'][entity_type] += 1
        
        # 计算统计信息
        analysis['statistics'] = {
            'total_entities': drawing.entity_count,
            'total_layers': len(drawing.layers),
            'active_layers': sum(1 for layer in drawing.layers if layer.visible),
            'locked_layers': sum(1 for layer in drawing.layers if layer.locked),
            'drawing_area': (drawing.bounds.max_x - drawing.bounds.min_x) * (drawing.bounds.max_y - drawing.bounds.min_y)
        }
        
        return analysis
    
    @staticmethod
    def extract_points(drawing: CADDrawing, layer_filter: Optional[List[str]] = None) -> List[TerrainPoint]:
        """从CAD图纸中提取点"""
        points = []
        
        for layer in drawing.layers:
            if layer_filter and layer.name not in layer_filter:
                continue
            
            for entity in layer.entities:
                if entity.entity_type == CADEntityType.POINT:
                    loc = entity.geometry['location']
                    points.append(TerrainPoint(loc[0], loc[1], loc[2]))
                
                elif entity.entity_type == CADEntityType.LINE:
                    start = entity.geometry['start']
                    end = entity.geometry['end']
                    points.append(TerrainPoint(start[0], start[1], start[2]))
                    points.append(TerrainPoint(end[0], end[1], end[2]))
                
                elif entity.entity_type == CADEntityType.POLYLINE:
                    for point in entity.geometry['points']:
                        points.append(TerrainPoint(point[0], point[1], point[2]))
        
        return points
    
    @staticmethod
    def extract_lines(drawing: CADDrawing, layer_filter: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """从CAD图纸中提取线条"""
        lines = []
        
        for layer in drawing.layers:
            if layer_filter and layer.name not in layer_filter:
                continue
            
            for entity in layer.entities:
                if entity.entity_type == CADEntityType.LINE:
                    lines.append({
                        'type': 'line',
                        'layer': layer.name,
                        'start': entity.geometry['start'],
                        'end': entity.geometry['end'],
                        'length': math.sqrt(
                            (entity.geometry['end'][0] - entity.geometry['start'][0])**2 +
                            (entity.geometry['end'][1] - entity.geometry['start'][1])**2 +
                            (entity.geometry['end'][2] - entity.geometry['start'][2])**2
                        )
                    })
                
                elif entity.entity_type == CADEntityType.POLYLINE:
                    points = entity.geometry['points']
                    total_length = 0
                    
                    for i in range(len(points) - 1):
                        segment_length = math.sqrt(
                            (points[i+1][0] - points[i][0])**2 +
                            (points[i+1][1] - points[i][1])**2 +
                            (points[i+1][2] - points[i][2])**2
                        )
                        total_length += segment_length
                    
                    lines.append({
                        'type': 'polyline',
                        'layer': layer.name,
                        'points': points,
                        'closed': entity.geometry['closed'],
                        'length': total_length
                    })
        
        return lines


# 工厂函数
def create_cad_importer() -> CADImporter:
    """创建CAD导入器"""
    return CADImporter()


def import_cad_file(file_path: str) -> ImportResult:
    """导入CAD文件的便捷函数"""
    importer = create_cad_importer()
    return importer.import_cad_file(file_path)


def analyze_cad_drawing(drawing: CADDrawing) -> Dict[str, Any]:
    """分析CAD图纸的便捷函数"""
    return CADAnalyzer.analyze_drawing(drawing)


def get_supported_cad_formats() -> List[str]:
    """获取支持的CAD格式"""
    importer = create_cad_importer()
    return importer.get_supported_formats()


# 示例数据生成
def create_sample_dxf_content() -> str:
    """创建示例DXF内容"""
    if not EZDXF_AVAILABLE:
        return ""
    
    # 创建新的DXF文档
    doc = ezdxf.new('R2010')
    msp = doc.modelspace()
    
    # 添加一些示例实体
    # 添加线条
    msp.add_line((0, 0), (100, 100))
    msp.add_line((100, 0), (0, 100))
    
    # 添加圆
    msp.add_circle((50, 50), 25)
    
    # 添加多段线
    points = [(0, 0), (50, 0), (50, 50), (0, 50), (0, 0)]
    msp.add_lwpolyline(points)
    
    # 添加文字
    msp.add_text("Sample Text", dxfattribs={'insert': (25, 75), 'height': 5})
    
    # 添加点
    msp.add_point((25, 25))
    
    return doc


def create_sample_dxf_file(file_path: str) -> str:
    """创建示例DXF文件"""
    if not EZDXF_AVAILABLE:
        # 创建一个简单的DXF文件内容
        dxf_content = """0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
0
ENDSEC
0
SECTION
2
TABLES
0
TABLE
2
LAYER
70
1
0
LAYER
2
0
70
0
62
7
6
CONTINUOUS
0
ENDTAB
0
ENDSEC
0
SECTION
2
ENTITIES
0
LINE
8
0
10
0.0
20
0.0
30
0.0
11
100.0
21
100.0
31
0.0
0
CIRCLE
8
0
10
50.0
20
50.0
30
0.0
40
25.0
0
ENDSEC
0
EOF
"""
        with open(file_path, 'w') as f:
            f.write(dxf_content)
    else:
        doc = create_sample_dxf_content()
        doc.saveas(file_path)
    
    return file_path


@dataclass
class ExportResult:
    """导出结果"""
    success: bool
    message: str
    file_path: Optional[str] = None
    entity_count: int = 0
    layer_count: int = 0
    file_size: int = 0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []


class CADExporter:
    """CAD数据导出器"""
    
    def __init__(self):
        self.supported_formats = {'.dxf': self._export_dxf}
    
    def export_drawing(self, drawing: CADDrawing, output_path: str, 
                      format_options: Optional[Dict[str, Any]] = None) -> ExportResult:
        """导出CAD图纸"""
        if not EZDXF_AVAILABLE:
            return ExportResult(False, "需要安装ezdxf库来导出CAD文件")
        
        file_ext = Path(output_path).suffix.lower()
        if file_ext not in self.supported_formats:
            return ExportResult(False, f"不支持的导出格式: {file_ext}")
        
        try:
            export_func = self.supported_formats[file_ext]
            return export_func(drawing, output_path, format_options or {})
        except Exception as e:
            return ExportResult(False, f"导出失败: {str(e)}")
    
    def _export_dxf(self, drawing: CADDrawing, output_path: str, 
                   options: Dict[str, Any]) -> ExportResult:
        """导出为DXF格式"""
        try:
            # 创建新的DXF文档
            dxf_version = options.get('dxf_version', 'R2010')
            doc = ezdxf.new(dxf_version)
            
            # 设置单位
            if drawing.units == "Meters":
                doc.header['$INSUNITS'] = 6
            elif drawing.units == "Millimeters":
                doc.header['$INSUNITS'] = 4
            
            # 创建图层
            for layer in drawing.layers:
                if layer.name != '0':  # 0层默认存在
                    doc.layers.new(
                        name=layer.name,
                        dxfattribs={
                            'color': layer.color,
                            'linetype': layer.linetype,
                            'lineweight': int(layer.lineweight * 100),  # 转换为整数
                            'off': not layer.visible,
                            'lock': layer.locked
                        }
                    )
            
            # 获取模型空间
            msp = doc.modelspace()
            
            # 添加实体
            entity_count = 0
            for layer in drawing.layers:
                for entity in layer.entities:
                    try:
                        self._add_entity_to_modelspace(msp, entity)
                        entity_count += 1
                    except Exception as e:
                        # 跳过无法添加的实体
                        continue
            
            # 保存文件
            doc.saveas(output_path)
            
            # 获取文件大小
            file_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0
            
            return ExportResult(
                success=True,
                message=f"成功导出DXF文件，{entity_count}个实体",
                file_path=output_path,
                entity_count=entity_count,
                layer_count=len(drawing.layers),
                file_size=file_size
            )
            
        except Exception as e:
            return ExportResult(False, f"DXF导出失败: {str(e)}")
    
    def _add_entity_to_modelspace(self, msp, entity: CADEntity):
        """添加实体到模型空间"""
        dxfattribs = {
            'layer': entity.layer,
            'color': entity.color,
            'linetype': entity.linetype
        }
        
        if entity.entity_type == CADEntityType.LINE:
            start = entity.geometry['start']
            end = entity.geometry['end']
            msp.add_line(
                (start[0], start[1], start[2]),
                (end[0], end[1], end[2]),
                dxfattribs=dxfattribs
            )
        
        elif entity.entity_type == CADEntityType.CIRCLE:
            center = entity.geometry['center']
            radius = entity.geometry['radius']
            msp.add_circle(
                (center[0], center[1], center[2]),
                radius,
                dxfattribs=dxfattribs
            )
        
        elif entity.entity_type == CADEntityType.ARC:
            center = entity.geometry['center']
            radius = entity.geometry['radius']
            start_angle = entity.geometry['start_angle']
            end_angle = entity.geometry['end_angle']
            msp.add_arc(
                (center[0], center[1], center[2]),
                radius,
                start_angle,
                end_angle,
                dxfattribs=dxfattribs
            )
        
        elif entity.entity_type == CADEntityType.POLYLINE:
            points = entity.geometry['points']
            closed = entity.geometry.get('closed', False)
            
            # 转换为2D点（LWPolyline）
            points_2d = [(p[0], p[1]) for p in points]
            polyline = msp.add_lwpolyline(points_2d, dxfattribs=dxfattribs)
            if closed:
                polyline.close()
        
        elif entity.entity_type == CADEntityType.POINT:
            location = entity.geometry['location']
            msp.add_point(
                (location[0], location[1], location[2]),
                dxfattribs=dxfattribs
            )
        
        elif entity.entity_type == CADEntityType.TEXT:
            location = entity.geometry['location']
            text = entity.geometry['text']
            height = entity.geometry['height']
            rotation = entity.geometry.get('rotation', 0.0)
            
            text_dxfattribs = dxfattribs.copy()
            text_dxfattribs.update({
                'insert': (location[0], location[1], location[2]),
                'height': height,
                'rotation': rotation
            })
            
            msp.add_text(text, dxfattribs=text_dxfattribs)
    
    def export_road_design(self, road_design_data: Dict[str, Any], 
                          output_path: str) -> ExportResult:
        """导出道路设计数据为CAD格式"""
        if not EZDXF_AVAILABLE:
            return ExportResult(False, "需要安装ezdxf库来导出CAD文件")
        
        try:
            # 创建新的DXF文档
            doc = ezdxf.new('R2010')
            doc.header['$INSUNITS'] = 6  # 米
            
            # 创建专用图层
            layers_config = {
                'CENTERLINE': {'color': 1, 'linetype': 'CENTER'},  # 红色中心线
                'EDGE': {'color': 2, 'linetype': 'CONTINUOUS'},     # 黄色边线
                'CROSS_SECTION': {'color': 3, 'linetype': 'DASHED'}, # 绿色横断面
                'ANNOTATION': {'color': 7, 'linetype': 'CONTINUOUS'}, # 白色注释
                'DIMENSION': {'color': 6, 'linetype': 'CONTINUOUS'}   # 品红色尺寸
            }
            
            for layer_name, config in layers_config.items():
                doc.layers.new(
                    name=layer_name,
                    dxfattribs={
                        'color': config['color'],
                        'linetype': config['linetype']
                    }
                )
            
            msp = doc.modelspace()
            entity_count = 0
            
            # 导出道路中线
            if 'alignment' in road_design_data:
                alignment = road_design_data['alignment']
                entity_count += self._export_alignment(msp, alignment)
            
            # 导出横断面
            if 'cross_sections' in road_design_data:
                cross_sections = road_design_data['cross_sections']
                entity_count += self._export_cross_sections(msp, cross_sections)
            
            # 导出设计信息
            if 'design_info' in road_design_data:
                design_info = road_design_data['design_info']
                entity_count += self._export_design_info(msp, design_info)
            
            # 保存文件
            doc.saveas(output_path)
            
            file_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0
            
            return ExportResult(
                success=True,
                message=f"成功导出道路设计，{entity_count}个实体",
                file_path=output_path,
                entity_count=entity_count,
                layer_count=len(layers_config),
                file_size=file_size
            )
            
        except Exception as e:
            return ExportResult(False, f"道路设计导出失败: {str(e)}")
    
    def _export_alignment(self, msp, alignment: Dict[str, Any]) -> int:
        """导出道路中线"""
        entity_count = 0
        
        # 导出水平线形
        if 'horizontal_elements' in alignment:
            for element in alignment['horizontal_elements']:
                if element.get('type') == 'straight':
                    start = element['start_point']
                    end = element['end_point']
                    msp.add_line(
                        (start[0], start[1], 0),
                        (end[0], end[1], 0),
                        dxfattribs={'layer': 'CENTERLINE'}
                    )
                    entity_count += 1
                
                elif element.get('type') == 'circular':
                    center = element['center']
                    radius = element['radius']
                    start_angle = element.get('start_angle', 0)
                    end_angle = element.get('end_angle', 360)
                    
                    msp.add_arc(
                        (center[0], center[1], 0),
                        radius,
                        start_angle,
                        end_angle,
                        dxfattribs={'layer': 'CENTERLINE'}
                    )
                    entity_count += 1
        
        # 导出桩号标注
        if 'stations' in alignment:
            for station_data in alignment['stations']:
                station = station_data['station']
                point = station_data['point']
                
                # 添加桩号文字
                msp.add_text(
                    f"K{station/1000:.3f}",
                    dxfattribs={
                        'layer': 'ANNOTATION',
                        'insert': (point[0], point[1] + 2, 0),
                        'height': 2.0
                    }
                )
                entity_count += 1
        
        return entity_count
    
    def _export_cross_sections(self, msp, cross_sections: List[Dict[str, Any]]) -> int:
        """导出横断面"""
        entity_count = 0
        
        for section in cross_sections:
            station = section.get('station', 0)
            
            # 导出地面线
            if 'ground_profile' in section:
                points = section['ground_profile']
                if len(points) > 1:
                    for i in range(len(points) - 1):
                        msp.add_line(
                            (points[i][0], points[i][1], 0),
                            (points[i+1][0], points[i+1][1], 0),
                            dxfattribs={'layer': 'CROSS_SECTION', 'color': 8}
                        )
                        entity_count += 1
            
            # 导出设计线
            if 'design_profile' in section:
                points = section['design_profile']
                if len(points) > 1:
                    for i in range(len(points) - 1):
                        msp.add_line(
                            (points[i][0], points[i][1], 0),
                            (points[i+1][0], points[i+1][1], 0),
                            dxfattribs={'layer': 'CROSS_SECTION', 'color': 2}
                        )
                        entity_count += 1
            
            # 添加桩号标注
            msp.add_text(
                f"K{station/1000:.3f}",
                dxfattribs={
                    'layer': 'ANNOTATION',
                    'insert': (0, -10, 0),
                    'height': 1.5
                }
            )
            entity_count += 1
        
        return entity_count
    
    def _export_design_info(self, msp, design_info: Dict[str, Any]) -> int:
        """导出设计信息"""
        entity_count = 0
        
        # 创建图框和标题栏
        title = design_info.get('title', '道路设计图')
        designer = design_info.get('designer', '')
        date = design_info.get('date', '')
        scale = design_info.get('scale', '1:1000')
        
        # 添加标题
        msp.add_text(
            title,
            dxfattribs={
                'layer': 'ANNOTATION',
                'insert': (0, 0, 0),
                'height': 5.0,
                'style': 'STANDARD'
            }
        )
        entity_count += 1
        
        # 添加设计信息
        info_text = f"设计: {designer}  日期: {date}  比例: {scale}"
        msp.add_text(
            info_text,
            dxfattribs={
                'layer': 'ANNOTATION',
                'insert': (0, -8, 0),
                'height': 2.5
            }
        )
        entity_count += 1
        
        return entity_count
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的导出格式"""
        return list(self.supported_formats.keys())


class CADConverter:
    """CAD数据转换器"""
    
    def __init__(self):
        self.importer = CADImporter()
        self.exporter = CADExporter()
    
    def convert_terrain_to_cad(self, terrain_points: List[TerrainPoint], 
                              output_path: str, layer_name: str = "TERRAIN") -> ExportResult:
        """将地形点转换为CAD格式"""
        # 创建CAD图纸
        layers = [CADLayer(name=layer_name, color=3)]  # 绿色
        entities = []
        
        # 将地形点转换为CAD点实体
        for point in terrain_points:
            entity = CADEntity(
                entity_type=CADEntityType.POINT,
                layer=layer_name,
                color=3,
                geometry={'location': [point.x, point.y, point.z]}
            )
            entities.append(entity)
        
        layers[0].entities = entities
        
        # 计算边界
        if terrain_points:
            xs = [p.x for p in terrain_points]
            ys = [p.y for p in terrain_points]
            zs = [p.z for p in terrain_points]
            bounds = BoundingBox3D(min(xs), min(ys), min(zs), max(xs), max(ys), max(zs))
        else:
            bounds = BoundingBox3D(0, 0, 0, 0, 0, 0)
        
        drawing = CADDrawing(
            filename=Path(output_path).name,
            layers=layers,
            bounds=bounds,
            entity_count=len(entities),
            units="Meters"
        )
        
        # 导出
        return self.exporter.export_drawing(drawing, output_path)
    
    def convert_lines_to_cad(self, lines: List[Dict[str, Any]], 
                           output_path: str, layer_name: str = "LINES") -> ExportResult:
        """将线条数据转换为CAD格式"""
        layers = [CADLayer(name=layer_name, color=1)]  # 红色
        entities = []
        
        for line in lines:
            if line['type'] == 'line':
                entity = CADEntity(
                    entity_type=CADEntityType.LINE,
                    layer=layer_name,
                    color=1,
                    geometry={
                        'start': line['start'],
                        'end': line['end']
                    }
                )
                entities.append(entity)
            
            elif line['type'] == 'polyline':
                entity = CADEntity(
                    entity_type=CADEntityType.POLYLINE,
                    layer=layer_name,
                    color=1,
                    geometry={
                        'points': line['points'],
                        'closed': line.get('closed', False)
                    }
                )
                entities.append(entity)
        
        layers[0].entities = entities
        
        # 计算边界（简化）
        bounds = BoundingBox3D(0, 0, 0, 100, 100, 0)
        
        drawing = CADDrawing(
            filename=Path(output_path).name,
            layers=layers,
            bounds=bounds,
            entity_count=len(entities),
            units="Meters"
        )
        
        return self.exporter.export_drawing(drawing, output_path)


# 扩展工厂函数
def create_cad_exporter() -> CADExporter:
    """创建CAD导出器"""
    return CADExporter()


def create_cad_converter() -> CADConverter:
    """创建CAD转换器"""
    return CADConverter()


def export_road_design_to_cad(road_design_data: Dict[str, Any], output_path: str) -> ExportResult:
    """导出道路设计为CAD格式的便捷函数"""
    exporter = create_cad_exporter()
    return exporter.export_road_design(road_design_data, output_path)


def convert_terrain_to_cad(terrain_points: List[TerrainPoint], output_path: str) -> ExportResult:
    """将地形点转换为CAD格式的便捷函数"""
    converter = create_cad_converter()
    return converter.convert_terrain_to_cad(terrain_points, output_path)