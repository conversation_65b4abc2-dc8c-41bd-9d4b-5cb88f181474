"""
GIS数据处理服务
GIS Data Processing Service
"""

import os
import json
import csv
import struct
from typing import List, Dict, Any, Optional, Tuple, Union
from pathlib import Path
import tempfile
from dataclasses import dataclass

# 尝试导入numpy，如果不可用则使用标准库
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

# 尝试导入模型
try:
    from src.models.gis_data import TerrainModel, GeospatialFeature, BoundingBox3D
    MODELS_AVAILABLE = True
except ImportError:
    MODELS_AVAILABLE = False
    # 定义简化的BoundingBox3D类
    class BoundingBox3D:
        def __init__(self, min_x: float, min_y: float, min_z: float,
                     max_x: float, max_y: float, max_z: float):
            self.min_x = min_x
            self.min_y = min_y
            self.min_z = min_z
            self.max_x = max_x
            self.max_y = max_y
            self.max_z = max_z


@dataclass
class TerrainPoint:
    """地形点"""
    x: float
    y: float
    z: float
    attributes: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.attributes is None:
            self.attributes = {}


@dataclass
class ImportResult:
    """导入结果"""
    success: bool
    message: str
    point_count: int = 0
    bounds: Optional[BoundingBox3D] = None
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []


class TerrainDataImporter:
    """地形数据导入器"""
    
    def __init__(self):
        self.supported_formats = {
            '.xyz': self._import_xyz,
            '.csv': self._import_csv,
            '.txt': self._import_text,
            '.dem': self._import_dem,
            '.asc': self._import_ascii_grid,
            '.json': self._import_geojson
        }
    
    def import_terrain_data(self, file_path: str, format_hint: Optional[str] = None) -> ImportResult:
        """导入地形数据"""
        if not os.path.exists(file_path):
            return ImportResult(False, f"文件不存在: {file_path}")
        
        # 确定文件格式
        file_ext = Path(file_path).suffix.lower()
        if format_hint:
            file_ext = format_hint.lower()
            if not file_ext.startswith('.'):
                file_ext = '.' + file_ext
        
        if file_ext not in self.supported_formats:
            return ImportResult(False, f"不支持的文件格式: {file_ext}")
        
        try:
            # 调用相应的导入函数
            import_func = self.supported_formats[file_ext]
            return import_func(file_path)
            
        except Exception as e:
            return ImportResult(False, f"导入失败: {str(e)}")
    
    def _import_xyz(self, file_path: str) -> ImportResult:
        """导入XYZ格式文件"""
        points = []
        errors = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    try:
                        parts = line.split()
                        if len(parts) >= 3:
                            x = float(parts[0])
                            y = float(parts[1])
                            z = float(parts[2])
                            
                            # 额外属性
                            attributes = {}
                            if len(parts) > 3:
                                for i, value in enumerate(parts[3:], 4):
                                    try:
                                        attributes[f'attr_{i}'] = float(value)
                                    except ValueError:
                                        attributes[f'attr_{i}'] = value
                            
                            points.append(TerrainPoint(x, y, z, attributes))
                        else:
                            errors.append(f"第{line_num}行格式错误: {line}")
                            
                    except ValueError as e:
                        errors.append(f"第{line_num}行数据错误: {e}")
            
            if not points:
                return ImportResult(False, "未找到有效的地形点数据")
            
            bounds = self._calculate_bounds(points)
            message = f"成功导入{len(points)}个地形点"
            if errors:
                message += f"，{len(errors)}个错误"
            
            return ImportResult(True, message, len(points), bounds, errors)
            
        except Exception as e:
            return ImportResult(False, f"读取XYZ文件失败: {e}")
    
    def _import_csv(self, file_path: str) -> ImportResult:
        """导入CSV格式文件"""
        points = []
        errors = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # 尝试检测分隔符
                sample = f.read(1024)
                f.seek(0)
                
                delimiter = ','
                if '\t' in sample:
                    delimiter = '\t'
                elif ';' in sample:
                    delimiter = ';'
                
                reader = csv.reader(f, delimiter=delimiter)
                headers = None
                
                for row_num, row in enumerate(reader, 1):
                    if not row:
                        continue
                    
                    # 第一行可能是标题
                    if headers is None:
                        try:
                            # 尝试解析为数字，如果失败则认为是标题行
                            float(row[0])
                            headers = [f'col_{i}' for i in range(len(row))]
                        except (ValueError, IndexError):
                            headers = row
                            continue
                    
                    try:
                        if len(row) >= 3:
                            x = float(row[0])
                            y = float(row[1])
                            z = float(row[2])
                            
                            # 额外属性
                            attributes = {}
                            for i, value in enumerate(row[3:], 3):
                                if i < len(headers):
                                    key = headers[i]
                                else:
                                    key = f'col_{i}'
                                
                                try:
                                    attributes[key] = float(value)
                                except ValueError:
                                    attributes[key] = value
                            
                            points.append(TerrainPoint(x, y, z, attributes))
                        else:
                            errors.append(f"第{row_num}行数据不足: {row}")
                            
                    except ValueError as e:
                        errors.append(f"第{row_num}行数据错误: {e}")
            
            if not points:
                return ImportResult(False, "未找到有效的地形点数据")
            
            bounds = self._calculate_bounds(points)
            message = f"成功导入{len(points)}个地形点"
            if errors:
                message += f"，{len(errors)}个错误"
            
            return ImportResult(True, message, len(points), bounds, errors)
            
        except Exception as e:
            return ImportResult(False, f"读取CSV文件失败: {e}")
    
    def _import_text(self, file_path: str) -> ImportResult:
        """导入文本格式文件（与XYZ相同）"""
        return self._import_xyz(file_path)
    
    def _import_dem(self, file_path: str) -> ImportResult:
        """导入DEM格式文件（简化实现）"""
        # 这里实现一个简化的DEM读取
        # 实际应用中需要使用GDAL等专业库
        return ImportResult(False, "DEM格式需要GDAL库支持，当前为简化实现")
    
    def _import_ascii_grid(self, file_path: str) -> ImportResult:
        """导入ASCII Grid格式文件"""
        points = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # 读取头部信息
                headers = {}
                for _ in range(6):  # 通常有6行头部信息
                    line = f.readline().strip()
                    if line:
                        parts = line.split()
                        if len(parts) >= 2:
                            key = parts[0].lower()
                            value = parts[1]
                            try:
                                headers[key] = float(value)
                            except ValueError:
                                headers[key] = value
                
                # 检查必要的头部信息
                required_keys = ['ncols', 'nrows', 'xllcorner', 'yllcorner', 'cellsize']
                for key in required_keys:
                    if key not in headers:
                        return ImportResult(False, f"缺少必要的头部信息: {key}")
                
                ncols = int(headers['ncols'])
                nrows = int(headers['nrows'])
                xllcorner = headers['xllcorner']
                yllcorner = headers['yllcorner']
                cellsize = headers['cellsize']
                nodata_value = headers.get('nodata_value', -9999)
                
                # 读取数据
                for row in range(nrows):
                    line = f.readline().strip()
                    if line:
                        values = line.split()
                        for col, value_str in enumerate(values):
                            try:
                                z = float(value_str)
                                if z != nodata_value:
                                    x = xllcorner + col * cellsize + cellsize / 2
                                    y = yllcorner + (nrows - row - 1) * cellsize + cellsize / 2
                                    points.append(TerrainPoint(x, y, z))
                            except ValueError:
                                continue
            
            if not points:
                return ImportResult(False, "未找到有效的地形点数据")
            
            bounds = self._calculate_bounds(points)
            return ImportResult(True, f"成功导入{len(points)}个地形点", len(points), bounds)
            
        except Exception as e:
            return ImportResult(False, f"读取ASCII Grid文件失败: {e}")
    
    def _import_geojson(self, file_path: str) -> ImportResult:
        """导入GeoJSON格式文件"""
        points = []
        errors = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if data.get('type') != 'FeatureCollection':
                return ImportResult(False, "不是有效的GeoJSON FeatureCollection")
            
            features = data.get('features', [])
            
            for i, feature in enumerate(features):
                try:
                    geometry = feature.get('geometry', {})
                    properties = feature.get('properties', {})
                    
                    if geometry.get('type') == 'Point':
                        coords = geometry.get('coordinates', [])
                        if len(coords) >= 2:
                            x = coords[0]
                            y = coords[1]
                            z = coords[2] if len(coords) > 2 else properties.get('elevation', 0.0)
                            
                            points.append(TerrainPoint(x, y, z, properties))
                        else:
                            errors.append(f"要素{i}: 坐标数据不足")
                    
                    elif geometry.get('type') in ['MultiPoint', 'LineString', 'Polygon']:
                        # 从复杂几何中提取点
                        coords = geometry.get('coordinates', [])
                        extracted_points = self._extract_points_from_geometry(coords, geometry.get('type'))
                        
                        for x, y, z in extracted_points:
                            points.append(TerrainPoint(x, y, z, properties))
                
                except Exception as e:
                    errors.append(f"要素{i}: {e}")
            
            if not points:
                return ImportResult(False, "未找到有效的地形点数据")
            
            bounds = self._calculate_bounds(points)
            message = f"成功导入{len(points)}个地形点"
            if errors:
                message += f"，{len(errors)}个错误"
            
            return ImportResult(True, message, len(points), bounds, errors)
            
        except Exception as e:
            return ImportResult(False, f"读取GeoJSON文件失败: {e}")
    
    def _extract_points_from_geometry(self, coords: List, geom_type: str) -> List[Tuple[float, float, float]]:
        """从几何对象中提取点"""
        points = []
        
        if geom_type == 'MultiPoint':
            for coord in coords:
                if len(coord) >= 2:
                    x, y = coord[0], coord[1]
                    z = coord[2] if len(coord) > 2 else 0.0
                    points.append((x, y, z))
        
        elif geom_type == 'LineString':
            for coord in coords:
                if len(coord) >= 2:
                    x, y = coord[0], coord[1]
                    z = coord[2] if len(coord) > 2 else 0.0
                    points.append((x, y, z))
        
        elif geom_type == 'Polygon':
            # 只取外环的点
            if coords and len(coords[0]) > 0:
                for coord in coords[0]:
                    if len(coord) >= 2:
                        x, y = coord[0], coord[1]
                        z = coord[2] if len(coord) > 2 else 0.0
                        points.append((x, y, z))
        
        return points
    
    def _calculate_bounds(self, points: List[TerrainPoint]) -> BoundingBox3D:
        """计算边界框"""
        if not points:
            return None
        
        min_x = min_y = min_z = float('inf')
        max_x = max_y = max_z = float('-inf')
        
        for point in points:
            min_x = min(min_x, point.x)
            max_x = max(max_x, point.x)
            min_y = min(min_y, point.y)
            max_y = max(max_y, point.y)
            min_z = min(min_z, point.z)
            max_z = max(max_z, point.z)
        
        return BoundingBox3D(
            min_x=min_x, min_y=min_y, min_z=min_z,
            max_x=max_x, max_y=max_y, max_z=max_z
        )
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式"""
        return list(self.supported_formats.keys())
    
    def validate_file_format(self, file_path: str) -> Tuple[bool, str]:
        """验证文件格式"""
        if not os.path.exists(file_path):
            return False, "文件不存在"
        
        file_ext = Path(file_path).suffix.lower()
        if file_ext not in self.supported_formats:
            return False, f"不支持的文件格式: {file_ext}"
        
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        max_size = 100 * 1024 * 1024  # 100MB
        if file_size > max_size:
            return False, f"文件过大: {file_size / 1024 / 1024:.1f}MB，最大支持{max_size / 1024 / 1024}MB"
        
        return True, "文件格式有效"


class TerrainDataProcessor:
    """地形数据处理器"""
    
    def __init__(self):
        self.importer = TerrainDataImporter()
    
    def process_terrain_file(self, file_path: str, output_format: str = 'json',
                           simplify_tolerance: Optional[float] = None) -> Dict[str, Any]:
        """处理地形文件"""
        # 导入数据
        import_result = self.importer.import_terrain_data(file_path)
        
        if not import_result.success:
            return {
                'success': False,
                'message': import_result.message,
                'errors': import_result.errors
            }
        
        # 重新读取点数据（简化实现）
        points = self._load_points_from_file(file_path)
        
        # 数据处理
        processed_points = points
        if simplify_tolerance:
            processed_points = self._simplify_points(points, simplify_tolerance)
        
        # 生成输出
        result = {
            'success': True,
            'message': f'处理完成，共{len(processed_points)}个点',
            'point_count': len(processed_points),
            'bounds': import_result.bounds.__dict__ if import_result.bounds else None,
            'errors': import_result.errors
        }
        
        if output_format == 'json':
            result['points'] = [
                {'x': p.x, 'y': p.y, 'z': p.z, 'attributes': p.attributes}
                for p in processed_points
            ]
        elif output_format == 'xyz':
            result['xyz_data'] = '\n'.join([f"{p.x} {p.y} {p.z}" for p in processed_points])
        
        return result
    
    def _load_points_from_file(self, file_path: str) -> List[TerrainPoint]:
        """从文件加载点数据（简化实现）"""
        # 重新导入并解析点数据
        file_ext = Path(file_path).suffix.lower()
        points = []
        
        try:
            if file_ext == '.xyz' or file_ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if not line or line.startswith('#'):
                            continue
                        parts = line.split()
                        if len(parts) >= 3:
                            try:
                                x, y, z = float(parts[0]), float(parts[1]), float(parts[2])
                                points.append(TerrainPoint(x, y, z))
                            except ValueError:
                                continue
            
            elif file_ext == '.csv':
                import csv
                with open(file_path, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    headers = None
                    for row in reader:
                        if headers is None:
                            try:
                                float(row[0])
                                headers = [f'col_{i}' for i in range(len(row))]
                            except (ValueError, IndexError):
                                headers = row
                                continue
                        
                        if len(row) >= 3:
                            try:
                                x, y, z = float(row[0]), float(row[1]), float(row[2])
                                points.append(TerrainPoint(x, y, z))
                            except ValueError:
                                continue
        
        except Exception:
            pass
        
        return points
    
    def _simplify_points(self, points: List[TerrainPoint], tolerance: float) -> List[TerrainPoint]:
        """简化点数据"""
        if not points or tolerance <= 0:
            return points
        
        # 简单的网格简化算法
        simplified = []
        grid_size = tolerance
        
        # 创建网格字典
        grid_dict = {}
        
        for point in points:
            # 计算网格坐标
            grid_x = int(point.x / grid_size)
            grid_y = int(point.y / grid_size)
            grid_key = (grid_x, grid_y)
            
            # 如果网格中还没有点，或者当前点的高程更有代表性，则保留
            if grid_key not in grid_dict:
                grid_dict[grid_key] = point
            else:
                # 保留高程更极端的点（简化策略）
                existing_point = grid_dict[grid_key]
                if abs(point.z - existing_point.z) > tolerance:
                    # 如果高程差异较大，保留两个点
                    simplified.append(existing_point)
                    grid_dict[grid_key] = point
        
        # 添加所有网格中的代表点
        simplified.extend(grid_dict.values())
        
        return simplified
    
    def create_terrain_model(self, file_path: str, name: str, 
                           description: str = "") -> Optional[Dict[str, Any]]:
        """创建地形模型"""
        import_result = self.importer.import_terrain_data(file_path)
        
        if not import_result.success:
            return None
        
        # 计算分辨率（简化计算）
        resolution = 1.0
        if import_result.bounds:
            area = ((import_result.bounds.max_x - import_result.bounds.min_x) * 
                   (import_result.bounds.max_y - import_result.bounds.min_y))
            if import_result.point_count > 0:
                resolution = (area / import_result.point_count) ** 0.5
        
        terrain_model = {
            'name': name,
            'description': description,
            'bounds': import_result.bounds.__dict__ if import_result.bounds else None,
            'resolution': resolution,
            'coordinate_system': 'EPSG:4326',  # 默认坐标系
            'data_source': os.path.basename(file_path),
            'file_path': file_path,
            'file_format': Path(file_path).suffix.lower(),
            'point_count': import_result.point_count,
            'metadata': {
                'import_time': str(import_result),
                'errors': import_result.errors
            }
        }
        
        return terrain_model


# 工厂函数
def create_terrain_importer() -> TerrainDataImporter:
    """创建地形数据导入器"""
    return TerrainDataImporter()


def create_terrain_processor() -> TerrainDataProcessor:
    """创建地形数据处理器"""
    return TerrainDataProcessor()


def import_terrain_file(file_path: str, format_hint: Optional[str] = None) -> ImportResult:
    """导入地形文件的便捷函数"""
    importer = create_terrain_importer()
    return importer.import_terrain_data(file_path, format_hint)


def get_supported_terrain_formats() -> List[str]:
    """获取支持的地形数据格式"""
    importer = create_terrain_importer()
    return importer.get_supported_formats()


# 示例数据生成
def create_sample_xyz_file(file_path: str, grid_size: int = 10) -> str:
    """创建示例XYZ文件"""
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write("# Sample XYZ terrain data\n")
        f.write("# X Y Z\n")
        
        for i in range(grid_size):
            for j in range(grid_size):
                x = i * 10.0
                y = j * 10.0
                z = 100.0 + (i + j) * 0.5  # 简单的高程变化
                f.write(f"{x} {y} {z}\n")
    
    return file_path


def create_sample_csv_file(file_path: str, grid_size: int = 10) -> str:
    """创建示例CSV文件"""
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write("X,Y,Z,Type,Quality\n")
        
        for i in range(grid_size):
            for j in range(grid_size):
                x = i * 10.0
                y = j * 10.0
                z = 100.0 + (i + j) * 0.5
                point_type = "ground" if (i + j) % 2 == 0 else "vegetation"
                quality = "good" if z > 102 else "fair"
                f.write(f"{x},{y},{z},{point_type},{quality}\n")
    
    return file_path