"""
道路设计引擎
Road Design Engine
"""

from typing import List, Dict, Any, Optional, Tuple
import math
from dataclasses import dataclass

from src.core.geometry import (
    Point2D, Point3D, GeometryCalculator, 
    HorizontalAlignmentCalculator, VerticalAlignmentCalculator,
    StraightLineCalculator, CircularCurveCalculator
)

# 尝试导入模型，如果不可用则使用简化版本
try:
    from src.models.road_design import (
        RoadDesign, RoadAlignment, HorizontalElement, VerticalElement,
        CrossSection, DesignStandards, Point2D as ModelPoint2D
    )
    MODELS_AVAILABLE = True
except ImportError:
    MODELS_AVAILABLE = False


@dataclass
class DesignParameters:
    """设计参数"""
    design_speed: float = 40.0  # km/h
    min_horizontal_radius: float = 60.0  # m
    max_grade: float = 8.0  # %
    min_vertical_radius: float = 400.0  # m
    road_width: float = 7.0  # m
    shoulder_width: float = 1.0  # m
    superelevation_rate: float = 6.0  # %
    min_sight_distance: float = 40.0  # m


@dataclass
class AlignmentPoint:
    """线形点"""
    station: float
    x: float
    y: float
    z: float
    azimuth: float = 0.0
    grade: float = 0.0


class RoadDesignEngine:
    """道路设计引擎"""
    
    def __init__(self, design_parameters: Optional[DesignParameters] = None):
        self.parameters = design_parameters or DesignParameters()
        self.geometry_calc = GeometryCalculator()
        self.horizontal_calc = HorizontalAlignmentCalculator()
        self.vertical_calc = VerticalAlignmentCalculator()
        
    def create_alignment_from_control_points(self, control_points: List[Point3D],
                                           curve_radius: float = None) -> List[AlignmentPoint]:
        """从控制点创建道路中线"""
        if len(control_points) < 2:
            raise ValueError("至少需要2个控制点")
        
        alignment_points = []
        current_station = 0.0
        
        # 使用默认半径
        if curve_radius is None:
            curve_radius = self.parameters.min_horizontal_radius
        
        for i in range(len(control_points) - 1):
            start_point = control_points[i]
            end_point = control_points[i + 1]
            
            # 计算直线段
            start_2d = Point2D(start_point.x, start_point.y)
            end_2d = Point2D(end_point.x, end_point.y)
            
            # 计算线段长度和方位角
            segment_length = start_2d.distance_to(end_2d)
            azimuth = start_2d.angle_to(end_2d)
            
            # 计算高程变化
            elevation_diff = end_point.z - start_point.z
            grade = (elevation_diff / segment_length) * 100 if segment_length > 0 else 0
            
            # 生成线段上的点
            num_points = max(2, int(segment_length / 20.0))  # 每20m一个点
            
            for j in range(num_points):
                ratio = j / (num_points - 1) if num_points > 1 else 0
                
                # 插值计算坐标
                x = start_point.x + ratio * (end_point.x - start_point.x)
                y = start_point.y + ratio * (end_point.y - start_point.y)
                z = start_point.z + ratio * (end_point.z - start_point.z)
                
                station = current_station + ratio * segment_length
                
                alignment_point = AlignmentPoint(
                    station=station,
                    x=x, y=y, z=z,
                    azimuth=azimuth,
                    grade=grade
                )
                alignment_points.append(alignment_point)
            
            current_station += segment_length
        
        return alignment_points
    
    def optimize_horizontal_alignment(self, control_points: List[Point3D]) -> List[Dict[str, Any]]:
        """优化水平线形"""
        if len(control_points) < 3:
            return self._create_simple_alignment(control_points)
        
        elements = []
        
        for i in range(len(control_points) - 2):
            p1 = Point2D(control_points[i].x, control_points[i].y)
            p2 = Point2D(control_points[i + 1].x, control_points[i + 1].y)
            p3 = Point2D(control_points[i + 2].x, control_points[i + 2].y)
            
            # 计算转角
            angle1 = p1.angle_to(p2)
            angle2 = p2.angle_to(p3)
            deflection_angle = abs(angle2 - angle1)
            
            # 如果转角较小，使用直线连接
            if deflection_angle < math.radians(5):  # 5度
                if i == 0:  # 第一段
                    elements.append({
                        'type': 'straight',
                        'start_x': p1.x, 'start_y': p1.y,
                        'end_x': p2.x, 'end_y': p2.y,
                        'length': p1.distance_to(p2)
                    })
            else:
                # 使用圆曲线连接
                radius = max(self.parameters.min_horizontal_radius,
                           self._calculate_minimum_radius(deflection_angle))
                
                # 添加直线段
                if i == 0:
                    tangent_length = CircularCurveCalculator.calculate_tangent_length(
                        radius, deflection_angle
                    )
                    
                    # 计算切点
                    line_length = p1.distance_to(p2)
                    if tangent_length < line_length:
                        cut_point = self.geometry_calc.point_on_line(
                            p2, p1, tangent_length
                        )
                        
                        elements.append({
                            'type': 'straight',
                            'start_x': p1.x, 'start_y': p1.y,
                            'end_x': cut_point.x, 'end_y': cut_point.y,
                            'length': p1.distance_to(cut_point)
                        })
                
                # 添加圆曲线段
                try:
                    center = CircularCurveCalculator.calculate_center(
                        p1, p3, radius, clockwise=True
                    )
                    
                    start_angle = center.angle_to(p2) - deflection_angle / 2
                    end_angle = center.angle_to(p2) + deflection_angle / 2
                    
                    elements.append({
                        'type': 'circular',
                        'center_x': center.x, 'center_y': center.y,
                        'radius': radius,
                        'start_angle': start_angle,
                        'end_angle': end_angle,
                        'length': CircularCurveCalculator.calculate_arc_length(
                            radius, deflection_angle
                        )
                    })
                except ValueError:
                    # 如果无法创建圆曲线，使用直线
                    elements.append({
                        'type': 'straight',
                        'start_x': p2.x, 'start_y': p2.y,
                        'end_x': p3.x, 'end_y': p3.y,
                        'length': p2.distance_to(p3)
                    })
        
        return elements
    
    def _create_simple_alignment(self, control_points: List[Point3D]) -> List[Dict[str, Any]]:
        """创建简单线形（直线连接）"""
        elements = []
        
        for i in range(len(control_points) - 1):
            start = control_points[i]
            end = control_points[i + 1]
            
            elements.append({
                'type': 'straight',
                'start_x': start.x, 'start_y': start.y,
                'end_x': end.x, 'end_y': end.y,
                'length': Point2D(start.x, start.y).distance_to(Point2D(end.x, end.y))
            })
        
        return elements
    
    def _calculate_minimum_radius(self, deflection_angle: float) -> float:
        """根据转角计算最小半径"""
        # 基于设计速度和超高计算最小半径
        v = self.parameters.design_speed  # km/h
        e = self.parameters.superelevation_rate / 100  # 转换为小数
        f = 0.15  # 横向摩擦系数
        
        # R = V²/(127(e + f))
        min_radius = (v * v) / (127 * (e + f))
        
        # 考虑转角影响
        angle_factor = 1.0 + abs(deflection_angle) / math.pi
        
        return max(self.parameters.min_horizontal_radius, min_radius * angle_factor)
    
    def validate_horizontal_alignment(self, elements: List[Dict[str, Any]]) -> List[str]:
        """验证水平线形"""
        errors = []
        
        for i, element in enumerate(elements):
            if element['type'] == 'circular':
                radius = element.get('radius', 0)
                if radius < self.parameters.min_horizontal_radius:
                    errors.append(f"元素{i}: 圆曲线半径{radius:.1f}m小于最小值{self.parameters.min_horizontal_radius}m")
            
            # 检查长度
            length = element.get('length', 0)
            if length <= 0:
                errors.append(f"元素{i}: 长度无效({length})")
        
        # 检查连续性
        continuity_errors = self.horizontal_calc.validate_alignment_continuity(elements)
        errors.extend(continuity_errors)
        
        return errors
    
    def calculate_sight_distance(self, alignment_points: List[AlignmentPoint],
                               eye_height: float = 1.2, object_height: float = 0.1) -> List[float]:
        """计算视距"""
        sight_distances = []
        
        for i, point in enumerate(alignment_points):
            max_sight_distance = 0.0
            
            # 向前搜索可见距离
            for j in range(i + 1, len(alignment_points)):
                target_point = alignment_points[j]
                distance = abs(target_point.station - point.station)
                
                # 简化的视距计算（实际应考虑地形遮挡）
                elevation_diff = abs(target_point.z - point.z)
                sight_angle = math.atan2(elevation_diff, distance) if distance > 0 else 0
                
                # 检查是否被中间点遮挡
                blocked = False
                for k in range(i + 1, j):
                    mid_point = alignment_points[k]
                    mid_distance = abs(mid_point.station - point.station)
                    required_height = point.z + eye_height + (sight_angle * mid_distance)
                    
                    if mid_point.z + object_height > required_height:
                        blocked = True
                        break
                
                if blocked:
                    break
                
                max_sight_distance = distance
                
                # 如果达到足够的视距就停止
                if max_sight_distance >= self.parameters.min_sight_distance * 2:
                    break
            
            sight_distances.append(max_sight_distance)
        
        return sight_distances
    
    def generate_stationing(self, alignment_points: List[AlignmentPoint],
                          interval: float = 20.0) -> List[AlignmentPoint]:
        """生成标准桩号"""
        if not alignment_points:
            return []
        
        # 计算总长度
        total_length = alignment_points[-1].station
        
        # 生成标准桩号点
        standard_points = []
        current_station = 0.0
        
        while current_station <= total_length:
            # 在原始点中插值
            interpolated_point = self._interpolate_alignment_point(
                alignment_points, current_station
            )
            standard_points.append(interpolated_point)
            current_station += interval
        
        # 确保包含终点
        if standard_points[-1].station < total_length:
            end_point = alignment_points[-1]
            standard_points.append(end_point)
        
        return standard_points
    
    def _interpolate_alignment_point(self, alignment_points: List[AlignmentPoint],
                                   target_station: float) -> AlignmentPoint:
        """在指定桩号插值计算点"""
        # 找到目标桩号所在的区间
        for i in range(len(alignment_points) - 1):
            if (alignment_points[i].station <= target_station <= 
                alignment_points[i + 1].station):
                
                p1 = alignment_points[i]
                p2 = alignment_points[i + 1]
                
                # 计算插值比例
                total_distance = p2.station - p1.station
                if total_distance == 0:
                    return p1
                
                ratio = (target_station - p1.station) / total_distance
                
                # 线性插值
                return AlignmentPoint(
                    station=target_station,
                    x=p1.x + ratio * (p2.x - p1.x),
                    y=p1.y + ratio * (p2.y - p1.y),
                    z=p1.z + ratio * (p2.z - p1.z),
                    azimuth=p1.azimuth + ratio * (p2.azimuth - p1.azimuth),
                    grade=p1.grade + ratio * (p2.grade - p1.grade)
                )
        
        # 如果超出范围，返回最近的点
        if target_station <= alignment_points[0].station:
            return alignment_points[0]
        else:
            return alignment_points[-1]


# 工厂函数
def create_road_design_engine(design_speed: float = 40.0) -> RoadDesignEngine:
    """创建道路设计引擎"""
    parameters = DesignParameters(design_speed=design_speed)
    return RoadDesignEngine(parameters)


def create_mining_road_parameters() -> DesignParameters:
    """创建露天矿山道路参数"""
    return DesignParameters(
        design_speed=40.0,
        min_horizontal_radius=60.0,
        max_grade=8.0,
        min_vertical_radius=400.0,
        road_width=7.0,
        shoulder_width=1.0,
        superelevation_rate=6.0,
        min_sight_distance=40.0
    )