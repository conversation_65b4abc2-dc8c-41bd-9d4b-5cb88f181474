#!/bin/bash
echo "============================================================"
echo "露天矿山道路设计软件 - 开发环境启动脚本"
echo "Mining Road Design Software - Development Startup Script"
echo "============================================================"

# 检查Python环境
echo "🔍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 错误: 未找到Python环境"
        echo "   请安装Python 3.8+并添加到PATH环境变量"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

$PYTHON_CMD --version

# 检查环境配置文件
if [ ! -f ".env" ]; then
    echo "🔧 创建环境配置文件..."
    cp ".env.example" ".env"
    echo "✅ 已创建 .env 文件"
fi

# 安装开发依赖
echo "📦 安装开发依赖包..."
$PYTHON_CMD -m pip install -r requirements_dev.txt
if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败，尝试安装基础依赖..."
    $PYTHON_CMD -m pip install fastapi uvicorn sqlalchemy pydantic python-dotenv
fi

# 设置环境变量
export PYTHONPATH=$(pwd)

# 创建必要目录
mkdir -p uploads logs static

# 启动应用
echo "🚀 启动应用服务器..."
echo "   访问地址: http://localhost:8000"
echo "   API文档: http://localhost:8000/docs"
echo "============================================================"
$PYTHON_CMD -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
