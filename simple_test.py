"""
简单的基础架构测试
Simple Infrastructure Test
"""

import os
import sys

def test_python_version():
    """测试Python版本"""
    version = sys.version_info
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return version.major >= 3 and version.minor >= 8

def test_project_structure():
    """测试项目结构"""
    required_files = [
        "src/main.py",
        "src/core/config.py", 
        "src/core/database.py",
        "src/api/v1/api.py",
        "requirements.txt",
        "Dockerfile",
        "docker-compose.yml"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ 文件存在: {file_path}")
        else:
            print(f"❌ 文件缺失: {file_path}")
            all_exist = False
    
    return all_exist

def test_directories():
    """测试目录结构"""
    required_dirs = [
        "src", "src/core", "src/api", "src/api/v1", "src/tasks",
        "tests", "uploads", "logs", "config", "config/design_standards"
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ 目录存在: {dir_path}")
        else:
            print(f"❌ 目录缺失: {dir_path}")
            all_exist = False
    
    return all_exist

def main():
    print("=" * 60)
    print("露天矿山道路设计软件 - 基础架构测试")
    print("Mining Road Design Software - Infrastructure Test")
    print("=" * 60)
    
    tests = [
        ("Python版本检查", test_python_version),
        ("项目文件检查", test_project_structure), 
        ("目录结构检查", test_directories)
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有基础架构测试通过！")
        print("✅ 项目基础架构搭建完成")
        print("\n下一步:")
        print("1. 安装完整依赖: python -m pip install -r requirements.txt")
        print("2. 配置环境变量: 复制 .env.example 为 .env")
        print("3. 启动应用: python -m uvicorn src.main:app --reload")
    else:
        print("❌ 部分测试失败，请检查项目配置")
    print("=" * 60)

if __name__ == "__main__":
    main()