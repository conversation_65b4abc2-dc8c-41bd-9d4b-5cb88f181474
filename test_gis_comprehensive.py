"""
GIS服务综合测试
Comprehensive GIS Service Test
"""

import sys
import os
import tempfile
import math

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.gis_service import (
    create_terrain_importer, create_sample_xyz_file, TerrainPoint
)
from src.services.surface_modeling import (
    create_tin_generator, create_grid_surface_generator, 
    create_sample_terrain_points, SurfaceAnalyzer
)
from src.services.spatial_analysis import (
    create_spatial_analyzer, create_terrain_analyzer,
    AnalysisType, analyze_buffer_zone, analyze_slope
)


def test_complete_gis_workflow():
    """测试完整的GIS工作流"""
    print("=" * 70)
    print("GIS服务综合测试 - 完整工作流")
    print("Comprehensive GIS Service Test - Complete Workflow")
    print("=" * 70)
    
    # 1. 数据导入测试
    print("\n🔍 步骤1: 地形数据导入")
    
    # 创建示例数据文件
    with tempfile.NamedTemporaryFile(suffix='.xyz', delete=False) as f:
        xyz_file = f.name
    
    try:
        create_sample_xyz_file(xyz_file, grid_size=8)
        
        # 导入数据
        importer = create_terrain_importer()
        import_result = importer.import_terrain_data(xyz_file)
        
        assert import_result.success, f"数据导入失败: {import_result.message}"
        print(f"✅ 成功导入 {import_result.point_count} 个地形点")
        print(f"   边界范围: ({import_result.bounds.min_x:.1f}, {import_result.bounds.min_y:.1f}) - ({import_result.bounds.max_x:.1f}, {import_result.bounds.max_y:.1f})")
        print(f"   高程范围: {import_result.bounds.min_z:.1f} - {import_result.bounds.max_z:.1f}")
        
    finally:
        os.unlink(xyz_file)
    
    # 2. 地表建模测试
    print("\n🔍 步骤2: 三维地表建模")
    
    # 创建示例地形点
    terrain_points = create_sample_terrain_points(grid_size=6)
    print(f"✅ 创建了 {len(terrain_points)} 个示例地形点")
    
    # 生成TIN地表
    tin_generator = create_tin_generator()
    tin_surface = tin_generator.generate_tin(terrain_points)
    
    print(f"✅ TIN地表生成成功:")
    print(f"   三角形数量: {len(tin_surface.triangles)}")
    print(f"   地表面积: {tin_surface.area:.2f} 平方米")
    
    # 生成网格地表
    grid_generator = create_grid_surface_generator(grid_size=5.0)
    grid_surface = grid_generator.generate_grid_surface(terrain_points, 'idw')
    
    print(f"✅ 网格地表生成成功:")
    print(f"   三角形数量: {len(grid_surface.triangles)}")
    print(f"   地表面积: {grid_surface.area:.2f} 平方米")
    
    # 3. 地表分析测试
    print("\n🔍 步骤3: 地表分析")
    
    # 高程插值测试
    test_x, test_y = 25.0, 25.0
    interpolated_elevation = SurfaceAnalyzer.interpolate_elevation(tin_surface, test_x, test_y)
    print(f"✅ 点({test_x}, {test_y})的插值高程: {interpolated_elevation:.2f}m")
    
    # 坡度计算测试
    slope, aspect = SurfaceAnalyzer.calculate_slope(tin_surface, test_x, test_y)
    print(f"✅ 点({test_x}, {test_y})的坡度: {slope:.1f}°, 坡向: {aspect:.1f}°")
    
    # 体积计算测试
    volume = SurfaceAnalyzer.calculate_volume(tin_surface, base_elevation=100.0)
    print(f"✅ 地表体积(基准面100m): {volume:.2f} 立方米")
    
    # 4. 空间分析测试
    print("\n🔍 步骤4: 空间分析")
    
    # 缓冲区分析
    test_points = [TerrainPoint(20.0, 20.0, 105.0), TerrainPoint(40.0, 40.0, 108.0)]
    buffer_result = analyze_buffer_zone(test_points, radius=15.0)
    
    assert buffer_result.success, f"缓冲区分析失败: {buffer_result.message}"
    print(f"✅ 缓冲区分析完成:")
    print(f"   缓冲区数量: {buffer_result.data['zone_count']}")
    print(f"   总面积: {buffer_result.data['total_area']:.2f} 平方米")
    
    # 坡度分析
    slope_result = analyze_slope(tin_surface, grid_size=8.0)
    
    assert slope_result.success, f"坡度分析失败: {slope_result.message}"
    slope_stats = slope_result.data['statistics']
    print(f"✅ 坡度分析完成:")
    print(f"   分析网格数: {slope_result.data['grid_count']}")
    print(f"   平均坡度: {slope_stats['avg_slope']:.1f}°")
    print(f"   最大坡度: {slope_stats['max_slope']:.1f}°")
    
    # 可视性分析
    analyzer = create_spatial_analyzer()
    observer_point = TerrainPoint(25.0, 25.0, 105.0)
    visibility_data = {
        'surface': tin_surface,
        'observer_point': observer_point,
        'observer_height': 1.7,
        'max_distance': 50.0
    }
    visibility_result = analyzer.analyze(AnalysisType.VISIBILITY, visibility_data)
    
    assert visibility_result.success, f"可视性分析失败: {visibility_result.message}"
    print(f"✅ 可视性分析完成:")
    print(f"   可见点数: {visibility_result.data['visible_count']}")
    print(f"   不可见点数: {visibility_result.data['invisible_count']}")
    print(f"   可视率: {visibility_result.data['visibility_ratio']:.2%}")
    
    # 5. 地形分析测试
    print("\n🔍 步骤5: 地形分析")
    
    terrain_analyzer = create_terrain_analyzer()
    
    # 地形坡度分析
    terrain_slope_result = terrain_analyzer.analyze_terrain_slope(tin_surface, grid_size=10.0)
    assert terrain_slope_result.success, "地形坡度分析失败"
    print(f"✅ 地形坡度分析完成，执行时间: {terrain_slope_result.execution_time:.3f}秒")
    
    # 流域分析
    pour_point = TerrainPoint(30.0, 30.0, 102.0)
    drainage_result = terrain_analyzer.analyze_drainage_basin(tin_surface, pour_point)
    print(f"✅ 流域分析完成:")
    print(f"   流域面积: {drainage_result['basin_area']} 平方米")
    print(f"   流域点数: {len(drainage_result['basin_points'])}")
    
    # 6. 性能测试
    print("\n🔍 步骤6: 性能测试")
    
    import time
    
    # 大数据集测试
    large_points = create_sample_terrain_points(grid_size=15)  # 225个点
    
    start_time = time.time()
    large_tin = tin_generator.generate_tin(large_points)
    tin_time = time.time() - start_time
    
    start_time = time.time()
    large_grid = grid_generator.generate_grid_surface(large_points, 'idw')
    grid_time = time.time() - start_time
    
    print(f"✅ 大数据集性能测试:")
    print(f"   数据点数: {len(large_points)}")
    print(f"   TIN生成时间: {tin_time:.3f}秒")
    print(f"   网格生成时间: {grid_time:.3f}秒")
    print(f"   TIN三角形数: {len(large_tin.triangles)}")
    print(f"   网格三角形数: {len(large_grid.triangles)}")
    
    print("\n" + "=" * 70)
    print("🎉 GIS服务综合测试全部通过！")
    print("✅ 所有GIS功能模块正常工作")
    print("✅ 数据导入 → 地表建模 → 空间分析 → 地形分析 工作流完整")
    print("=" * 70)
    
    return True


def test_integration_scenarios():
    """测试集成场景"""
    print("\n" + "=" * 70)
    print("GIS集成场景测试")
    print("=" * 70)
    
    # 场景1: 道路选线辅助分析
    print("\n🔍 场景1: 道路选线辅助分析")
    
    # 创建地形数据
    terrain_points = []
    for i in range(10):
        for j in range(10):
            x = i * 20.0
            y = j * 20.0
            # 创建一个有山谷的地形
            z = 100.0 + 10 * math.sin(i * 0.3) + 5 * math.cos(j * 0.4)
            terrain_points.append(TerrainPoint(x, y, z))
    
    # 生成地表
    tin_generator = create_tin_generator()
    terrain_surface = tin_generator.generate_tin(terrain_points)
    
    # 分析起点和终点之间的地形
    start_point = TerrainPoint(0.0, 0.0, 100.0)
    end_point = TerrainPoint(180.0, 180.0, 110.0)
    
    # 坡度分析
    analyzer = create_spatial_analyzer()
    slope_data = {'surface': terrain_surface, 'grid_size': 15.0}
    slope_result = analyzer.analyze(AnalysisType.SLOPE, slope_data)
    
    print(f"✅ 道路选线区域地形分析:")
    print(f"   平均坡度: {slope_result.data['statistics']['avg_slope']:.1f}°")
    print(f"   最大坡度: {slope_result.data['statistics']['max_slope']:.1f}°")
    
    # 可视性分析（用于确定观测点位置）
    visibility_data = {
        'surface': terrain_surface,
        'observer_point': start_point,
        'observer_height': 2.0,
        'max_distance': 100.0
    }
    visibility_result = analyzer.analyze(AnalysisType.VISIBILITY, visibility_data)
    print(f"   从起点的可视率: {visibility_result.data['visibility_ratio']:.2%}")
    
    # 场景2: 矿区环境影响分析
    print("\n🔍 场景2: 矿区环境影响分析")
    
    # 模拟矿区中心点
    mine_center = TerrainPoint(90.0, 90.0, 105.0)
    
    # 缓冲区分析（影响范围）
    buffer_data = {'points': [mine_center], 'radius': 50.0}
    buffer_result = analyzer.analyze(AnalysisType.BUFFER, buffer_data)
    
    print(f"✅ 矿区环境影响分析:")
    print(f"   影响范围面积: {buffer_result.data['total_area']:.2f} 平方米")
    
    # 地形分析
    terrain_analyzer = create_terrain_analyzer()
    drainage_result = terrain_analyzer.analyze_drainage_basin(terrain_surface, mine_center)
    print(f"   流域分析面积: {drainage_result['basin_area']} 平方米")
    
    print("\n✅ 所有集成场景测试通过！")
    
    return True


if __name__ == "__main__":
    try:
        success1 = test_complete_gis_workflow()
        success2 = test_integration_scenarios()
        
        if success1 and success2:
            print("\n🎉 GIS服务综合测试完全成功！")
            print("系统已准备好进行道路设计和空间分析工作。")
        else:
            print("\n❌ 部分测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()