"""
综合模型测试
Comprehensive Models Test
"""

def test_all_models():
    """测试所有数据模型"""
    print("=" * 70)
    print("露天矿山道路设计软件 - 数据模型综合测试")
    print("Mining Road Design Software - Comprehensive Models Test")
    print("=" * 70)
    
    success = True
    
    # 测试基础模型
    print("\n🔍 测试基础模型...")
    try:
        from src.models.base import BaseSchema, TimestampMixin, UUIDMixin
        from src.models.base import DesignStatus, GeometryType, CoordinateSystem
        print("✅ 基础模型导入成功")
        
        # 测试枚举
        print(f"   设计状态: {DesignStatus.DRAFT}, {DesignStatus.COMPLETED}")
        print(f"   几何类型: {GeometryType.POINT}, {GeometryType.LINE}")
        print(f"   坐标系统: {CoordinateSystem.WGS84}")
        
    except Exception as e:
        print(f"❌ 基础模型测试失败: {e}")
        success = False
    
    # 测试道路设计模型
    print("\n🔍 测试道路设计模型...")
    try:
        from src.models.road_design import (
            Point2D, Point3D, RoadDesign, DesignStandards,
            create_default_design_standards, create_sample_road_design
        )
        
        # 创建示例设计
        design = create_sample_road_design()
        print(f"✅ 道路设计创建成功: {design.name}")
        print(f"   设计ID: {str(design.id)[:8]}...")
        print(f"   设计标准: {design.design_standards.standard_name}")
        print(f"   设计速度: {design.design_standards.design_speed} km/h")
        
    except Exception as e:
        print(f"❌ 道路设计模型测试失败: {e}")
        success = False
    
    # 测试GIS数据模型
    print("\n🔍 测试GIS数据模型...")
    try:
        from src.models.gis_data import (
            Coordinate, Geometry, TerrainModel,
            create_point_geometry, create_sample_terrain_model
        )
        
        # 创建示例地形
        terrain = create_sample_terrain_model()
        print(f"✅ 地形模型创建成功: {terrain.name}")
        print(f"   地形ID: {str(terrain.id)[:8]}...")
        print(f"   分辨率: {terrain.resolution}m")
        print(f"   边界: {terrain.bounds.volume():.0f} 立方米")
        
        # 创建几何对象
        point_geom = create_point_geometry(100.0, 200.0, 50.0)
        print(f"✅ 点几何创建成功: {point_geom.coordinates}")
        
    except Exception as e:
        print(f"❌ GIS数据模型测试失败: {e}")
        success = False
    
    # 测试分析结果模型
    print("\n🔍 测试分析结果模型...")
    try:
        from src.models.analysis_results import (
            ConflictReport, OptimizationResult,
            create_sample_conflict_report, create_sample_optimization_result
        )
        from uuid import uuid4
        
        # 创建示例报告
        design_id = uuid4()
        conflict_report = create_sample_conflict_report(design_id, "测试设计")
        print(f"✅ 冲突报告创建成功: {len(conflict_report.conflicts)} 个冲突")
        print(f"   严重冲突: {conflict_report.severity_summary.critical_count}")
        print(f"   高级冲突: {conflict_report.severity_summary.high_count}")
        print(f"   中级冲突: {conflict_report.severity_summary.medium_count}")
        
        # 创建优化结果
        opt_result = create_sample_optimization_result(design_id, "测试设计")
        print(f"✅ 优化结果创建成功")
        print(f"   原始成本: ¥{opt_result.cost_comparison.original_cost:,.0f}")
        print(f"   优化成本: ¥{opt_result.cost_comparison.optimized_cost:,.0f}")
        print(f"   节省: {opt_result.cost_comparison.savings_percentage}%")
        
    except Exception as e:
        print(f"❌ 分析结果模型测试失败: {e}")
        success = False
    
    # 测试模型序列化
    print("\n🔍 测试模型序列化...")
    try:
        import json
        
        # 测试道路设计序列化
        design_dict = design.dict()
        design_json = json.dumps(design_dict, default=str, ensure_ascii=False, indent=2)
        print(f"✅ 道路设计序列化成功: {len(design_json)} 字符")
        
        # 测试冲突报告序列化
        report_dict = conflict_report.dict()
        report_json = json.dumps(report_dict, default=str, ensure_ascii=False, indent=2)
        print(f"✅ 冲突报告序列化成功: {len(report_json)} 字符")
        
    except Exception as e:
        print(f"❌ 模型序列化测试失败: {e}")
        success = False
    
    # 总结
    print("\n" + "=" * 70)
    if success:
        print("🎉 所有数据模型测试通过！")
        print("\n✅ 已完成的模型:")
        print("   • 基础数据模型 (BaseSchema, 枚举类型)")
        print("   • 道路设计模型 (RoadDesign, RoadAlignment, CrossSection)")
        print("   • GIS地理空间模型 (TerrainModel, GeospatialFeature, Layer)")
        print("   • 分析结果模型 (ConflictReport, SafetyReport, OptimizationResult)")
        print("\n🚀 数据模型层已完成，可以继续下一个任务！")
    else:
        print("❌ 部分模型测试失败，请检查配置")
    print("=" * 70)
    
    return success


if __name__ == "__main__":
    test_all_models()