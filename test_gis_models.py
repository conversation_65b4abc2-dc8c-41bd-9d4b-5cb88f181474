"""
GIS模型测试
GIS Models Test
"""

def test_gis_models():
    """测试GIS模型"""
    try:
        # 测试基础导入
        print("测试模型导入...")
        
        from src.models.gis_data import (
            Coordinate, Geometry, TerrainModel, GeospatialFeature,
            create_point_geometry, create_sample_terrain_model
        )
        from src.models.base import GeometryType
        
        print("✅ GIS模型导入成功")
        
        # 测试坐标
        coord = Coordinate(x=100.0, y=200.0, z=50.0)
        print(f"✅ 坐标创建成功: {coord.to_list()}")
        
        # 测试点几何
        point_geom = create_point_geometry(100.0, 200.0, 50.0)
        print(f"✅ 点几何创建成功: {point_geom.type}")
        
        # 测试GeoJSON转换
        geojson = point_geom.to_geojson()
        print(f"✅ GeoJSON转换成功: {geojson}")
        
        # 测试地形模型
        terrain = create_sample_terrain_model()
        print(f"✅ 地形模型创建成功: {terrain.name}")
        print(f"   边界: ({terrain.bounds.min_x}, {terrain.bounds.min_y}) - ({terrain.bounds.max_x}, {terrain.bounds.max_y})")
        print(f"   分辨率: {terrain.resolution}m")
        
        # 测试地理要素
        feature = GeospatialFeature(
            name="测试要素",
            geometry=point_geom,
            feature_type="control_point",
            properties={"elevation": 50.0, "type": "benchmark"}
        )
        print(f"✅ 地理要素创建成功: {feature.name}")
        
        # 测试GeoJSON Feature
        geojson_feature = feature.to_geojson_feature()
        print(f"✅ GeoJSON Feature转换成功")
        
        return True
        
    except Exception as e:
        print(f"❌ GIS模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_geometry_validation():
    """测试几何验证"""
    try:
        from src.models.gis_data import Geometry
        from src.models.base import GeometryType
        
        # 测试有效的点几何
        point = Geometry(
            type=GeometryType.POINT,
            coordinates=[100.0, 200.0]
        )
        print(f"✅ 点几何验证通过: {point.coordinates}")
        
        # 测试有效的线几何
        line = Geometry(
            type=GeometryType.LINE,
            coordinates=[[0.0, 0.0], [100.0, 100.0]]
        )
        print(f"✅ 线几何验证通过: {len(line.coordinates)} 个点")
        
        return True
        
    except Exception as e:
        print(f"❌ 几何验证测试失败: {e}")
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("GIS地理空间数据模型测试")
    print("=" * 60)
    
    success = True
    success &= test_gis_models()
    success &= test_geometry_validation()
    
    print("=" * 60)
    if success:
        print("🎉 所有GIS模型测试通过！")
    else:
        print("❌ 部分测试失败")
    print("=" * 60)