"""
GIS地理空间数据模型
GIS Geospatial Data Models
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from uuid import UUID
from pydantic import BaseModel, Field, validator
import json

from src.models.base import BaseSchema, TimestampMixin, UUIDMixin, GeometryType, CoordinateSystem

try:
    from sqlalchemy import Column, String, Text, Float, Integer, JSON, LargeBinary
    from src.models.base import SQLAlchemyBase
    # 尝试导入PostGIS相关
    try:
        from geoalchemy2 import Geography, Geometry as GeoGeometry
        POSTGIS_AVAILABLE = True
    except ImportError:
        POSTGIS_AVAILABLE = False
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    POSTGIS_AVAILABLE = False


# 几何数据类型
class Coordinate(BaseModel):
    """坐标点"""
    x: float
    y: float
    z: Optional[float] = None
    
    def to_list(self) -> List[float]:
        """转换为列表格式"""
        if self.z is not None:
            return [self.x, self.y, self.z]
        return [self.x, self.y]


class Geometry(BaseModel):
    """几何对象"""
    type: GeometryType = Field(..., description="几何类型")
    coordinates: Union[
        List[float],  # Point
        List[List[float]],  # LineString, MultiPoint
        List[List[List[float]]],  # Polygon, MultiLineString
        List[List[List[List[float]]]]  # MultiPolygon
    ] = Field(..., description="坐标数据")
    crs: str = Field(default=CoordinateSystem.WGS84, description="坐标参考系统")
    
    @validator('coordinates')
    def validate_coordinates(cls, v, values):
        """验证坐标数据格式"""
        geom_type = values.get('type')
        if not geom_type:
            return v
            
        # 基本验证逻辑
        if geom_type == GeometryType.POINT:
            if not isinstance(v, list) or len(v) < 2:
                raise ValueError("Point坐标必须至少包含x,y值")
        elif geom_type == GeometryType.LINE:
            if not isinstance(v, list) or len(v) < 2:
                raise ValueError("LineString必须至少包含2个点")
        
        return v
    
    def to_geojson(self) -> Dict[str, Any]:
        """转换为GeoJSON格式"""
        return {
            "type": self.type,
            "coordinates": self.coordinates
        }
    
    @classmethod
    def from_geojson(cls, geojson: Dict[str, Any], crs: str = CoordinateSystem.WGS84):
        """从GeoJSON创建几何对象"""
        return cls(
            type=geojson["type"].lower(),
            coordinates=geojson["coordinates"],
            crs=crs
        )


class BoundingBox3D(BaseModel):
    """三维边界框"""
    min_x: float
    min_y: float
    min_z: float
    max_x: float
    max_y: float
    max_z: float
    
    def contains_point(self, x: float, y: float, z: float) -> bool:
        """检查点是否在边界框内"""
        return (self.min_x <= x <= self.max_x and
                self.min_y <= y <= self.max_y and
                self.min_z <= z <= self.max_z)
    
    def volume(self) -> float:
        """计算体积"""
        return ((self.max_x - self.min_x) * 
                (self.max_y - self.min_y) * 
                (self.max_z - self.min_z))


class TerrainModel(BaseSchema, TimestampMixin, UUIDMixin):
    """地形模型"""
    name: str = Field(..., description="地形名称")
    description: Optional[str] = Field(None, description="描述")
    bounds: BoundingBox3D = Field(..., description="边界范围")
    resolution: float = Field(..., description="分辨率（米）")
    coordinate_system: str = Field(default=CoordinateSystem.WGS84, description="坐标系统")
    data_source: str = Field(..., description="数据来源")
    file_path: Optional[str] = Field(None, description="文件路径")
    file_format: str = Field(..., description="文件格式")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    @validator('resolution')
    def validate_resolution(cls, v):
        """验证分辨率"""
        if v <= 0:
            raise ValueError("分辨率必须大于0")
        return v


class GeospatialFeature(BaseSchema, TimestampMixin, UUIDMixin):
    """地理空间要素"""
    name: str = Field(..., description="要素名称")
    geometry: Geometry = Field(..., description="几何信息")
    properties: Dict[str, Any] = Field(default_factory=dict, description="属性数据")
    feature_type: str = Field(..., description="要素类型")
    layer_name: Optional[str] = Field(None, description="图层名称")
    style: Optional[Dict[str, Any]] = Field(None, description="样式信息")
    
    def to_geojson_feature(self) -> Dict[str, Any]:
        """转换为GeoJSON Feature格式"""
        return {
            "type": "Feature",
            "geometry": self.geometry.to_geojson(),
            "properties": {
                **self.properties,
                "id": str(self.id),
                "name": self.name,
                "feature_type": self.feature_type
            }
        }


class SpatialIndex(BaseModel):
    """空间索引"""
    index_type: str = Field(..., description="索引类型")
    bounds: BoundingBox3D = Field(..., description="索引范围")
    feature_count: int = Field(default=0, description="要素数量")
    index_data: Dict[str, Any] = Field(default_factory=dict, description="索引数据")


class Layer(BaseSchema, TimestampMixin, UUIDMixin):
    """图层"""
    name: str = Field(..., description="图层名称")
    description: Optional[str] = Field(None, description="描述")
    layer_type: str = Field(..., description="图层类型")
    coordinate_system: str = Field(default=CoordinateSystem.WGS84, description="坐标系统")
    features: List[GeospatialFeature] = Field(default_factory=list, description="要素列表")
    style: Dict[str, Any] = Field(default_factory=dict, description="图层样式")
    visible: bool = Field(default=True, description="是否可见")
    spatial_index: Optional[SpatialIndex] = Field(None, description="空间索引")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    def add_feature(self, feature: GeospatialFeature):
        """添加要素"""
        feature.layer_name = self.name
        self.features.append(feature)
    
    def get_bounds(self) -> Optional[BoundingBox3D]:
        """获取图层边界"""
        if not self.features:
            return None
        
        # 简化的边界计算
        min_x = min_y = min_z = float('inf')
        max_x = max_y = max_z = float('-inf')
        
        for feature in self.features:
            # 这里需要根据几何类型计算边界
            # 简化处理
            coords = feature.geometry.coordinates
            if feature.geometry.type == GeometryType.POINT:
                x, y = coords[0], coords[1]
                z = coords[2] if len(coords) > 2 else 0
                min_x, max_x = min(min_x, x), max(max_x, x)
                min_y, max_y = min(min_y, y), max(max_y, y)
                min_z, max_z = min(min_z, z), max(max_z, z)
        
        if min_x != float('inf'):
            return BoundingBox3D(
                min_x=min_x, min_y=min_y, min_z=min_z,
                max_x=max_x, max_y=max_y, max_z=max_z
            )
        return None
    
    def to_geojson(self) -> Dict[str, Any]:
        """转换为GeoJSON格式"""
        return {
            "type": "FeatureCollection",
            "name": self.name,
            "features": [feature.to_geojson_feature() for feature in self.features]
        }


class SpatialAnalysisResult(BaseSchema, TimestampMixin, UUIDMixin):
    """空间分析结果"""
    analysis_type: str = Field(..., description="分析类型")
    input_layers: List[str] = Field(..., description="输入图层")
    result_data: Dict[str, Any] = Field(..., description="结果数据")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="分析参数")
    execution_time: float = Field(..., description="执行时间（秒）")
    status: str = Field(default="completed", description="状态")
    error_message: Optional[str] = Field(None, description="错误信息")


# SQLAlchemy模型（如果可用）
if SQLALCHEMY_AVAILABLE:
    class TerrainModelDB(SQLAlchemyBase):
        """地形模型数据库表"""
        __tablename__ = "terrain_models"
        
        name = Column(String(255), nullable=False, index=True)
        description = Column(Text)
        bounds = Column(JSON, nullable=False)
        resolution = Column(Float, nullable=False)
        coordinate_system = Column(String(50), default=CoordinateSystem.WGS84)
        data_source = Column(String(255), nullable=False)
        file_path = Column(String(500))
        file_format = Column(String(50), nullable=False)
        metadata = Column(JSON, default=dict)
        
        def to_pydantic(self) -> TerrainModel:
            """转换为Pydantic模型"""
            data = self.to_dict()
            if data.get('bounds'):
                data['bounds'] = BoundingBox3D(**data['bounds'])
            return TerrainModel(**data)
    
    class GeospatialFeatureDB(SQLAlchemyBase):
        """地理空间要素数据库表"""
        __tablename__ = "geospatial_features"
        
        name = Column(String(255), nullable=False, index=True)
        geometry = Column(JSON, nullable=False)  # 存储为JSON，生产环境建议使用PostGIS
        properties = Column(JSON, default=dict)
        feature_type = Column(String(100), nullable=False, index=True)
        layer_name = Column(String(255), index=True)
        style = Column(JSON)
        
        # 如果有PostGIS，可以使用真正的几何字段
        if POSTGIS_AVAILABLE:
            geometry_postgis = Column(GeoGeometry('GEOMETRY', srid=4326))
        
        def to_pydantic(self) -> GeospatialFeature:
            """转换为Pydantic模型"""
            data = self.to_dict()
            if data.get('geometry'):
                data['geometry'] = Geometry(**data['geometry'])
            return GeospatialFeature(**data)
    
    class LayerDB(SQLAlchemyBase):
        """图层数据库表"""
        __tablename__ = "layers"
        
        name = Column(String(255), nullable=False, index=True)
        description = Column(Text)
        layer_type = Column(String(100), nullable=False)
        coordinate_system = Column(String(50), default=CoordinateSystem.WGS84)
        style = Column(JSON, default=dict)
        visible = Column(String(10), default="true")  # 存储为字符串
        spatial_index = Column(JSON)
        metadata = Column(JSON, default=dict)
        
        def to_pydantic(self) -> Layer:
            """转换为Pydantic模型"""
            data = self.to_dict()
            data['visible'] = data['visible'] == "true"
            if data.get('spatial_index'):
                data['spatial_index'] = SpatialIndex(**data['spatial_index'])
            # 注意：这里不包含features，需要单独查询
            data['features'] = []
            return Layer(**data)

else:
    TerrainModelDB = None
    GeospatialFeatureDB = None
    LayerDB = None


# 工厂函数
def create_point_geometry(x: float, y: float, z: Optional[float] = None) -> Geometry:
    """创建点几何"""
    coords = [x, y]
    if z is not None:
        coords.append(z)
    
    return Geometry(
        type=GeometryType.POINT,
        coordinates=coords
    )


def create_line_geometry(points: List[List[float]]) -> Geometry:
    """创建线几何"""
    return Geometry(
        type=GeometryType.LINE,
        coordinates=points
    )


def create_sample_terrain_model() -> TerrainModel:
    """创建示例地形模型"""
    return TerrainModel(
        name="示例地形",
        description="用于测试的示例地形数据",
        bounds=BoundingBox3D(
            min_x=0.0, min_y=0.0, min_z=0.0,
            max_x=1000.0, max_y=1000.0, max_z=100.0
        ),
        resolution=1.0,
        data_source="测试数据",
        file_format="DEM"
    )


def create_sample_layer() -> Layer:
    """创建示例图层"""
    # 创建一些示例要素
    point_feature = GeospatialFeature(
        name="测试点",
        geometry=create_point_geometry(100.0, 200.0, 50.0),
        feature_type="point",
        properties={"type": "control_point", "elevation": 50.0}
    )
    
    line_feature = GeospatialFeature(
        name="测试线",
        geometry=create_line_geometry([[0.0, 0.0], [100.0, 100.0], [200.0, 150.0]]),
        feature_type="line",
        properties={"type": "road_centerline", "length": 250.0}
    )
    
    layer = Layer(
        name="测试图层",
        description="用于测试的示例图层",
        layer_type="vector",
        features=[point_feature, line_feature]
    )
    
    return layer