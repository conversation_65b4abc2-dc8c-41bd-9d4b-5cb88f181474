"""
三维地表建模服务
3D Surface Modeling Service
"""

import math
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
import json

# 尝试导入numpy，如果不可用则使用标准库
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

from src.services.gis_service import TerrainPoint, BoundingBox3D


@dataclass
class Triangle:
    """三角形"""
    p1: TerrainPoint
    p2: TerrainPoint
    p3: TerrainPoint
    
    def area(self) -> float:
        """计算三角形面积"""
        # 使用向量叉积计算面积
        v1 = (self.p2.x - self.p1.x, self.p2.y - self.p1.y)
        v2 = (self.p3.x - self.p1.x, self.p3.y - self.p1.y)
        
        cross_product = v1[0] * v2[1] - v1[1] * v2[0]
        return abs(cross_product) / 2.0
    
    def contains_point(self, x: float, y: float) -> bool:
        """判断点是否在三角形内"""
        # 使用重心坐标法
        denom = ((self.p2.y - self.p3.y) * (self.p1.x - self.p3.x) + 
                (self.p3.x - self.p2.x) * (self.p1.y - self.p3.y))
        
        if abs(denom) < 1e-10:
            return False
        
        a = ((self.p2.y - self.p3.y) * (x - self.p3.x) + 
             (self.p3.x - self.p2.x) * (y - self.p3.y)) / denom
        b = ((self.p3.y - self.p1.y) * (x - self.p3.x) + 
             (self.p1.x - self.p3.x) * (y - self.p3.y)) / denom
        c = 1 - a - b
        
        return a >= 0 and b >= 0 and c >= 0
    
    def interpolate_elevation(self, x: float, y: float) -> float:
        """在三角形内插值高程"""
        if not self.contains_point(x, y):
            return 0.0
        
        # 使用重心坐标插值
        denom = ((self.p2.y - self.p3.y) * (self.p1.x - self.p3.x) + 
                (self.p3.x - self.p2.x) * (self.p1.y - self.p3.y))
        
        if abs(denom) < 1e-10:
            return (self.p1.z + self.p2.z + self.p3.z) / 3.0
        
        a = ((self.p2.y - self.p3.y) * (x - self.p3.x) + 
             (self.p3.x - self.p2.x) * (y - self.p3.y)) / denom
        b = ((self.p3.y - self.p1.y) * (x - self.p3.x) + 
             (self.p1.x - self.p3.x) * (y - self.p3.y)) / denom
        c = 1 - a - b
        
        return a * self.p1.z + b * self.p2.z + c * self.p3.z


@dataclass
class GridCell:
    """网格单元"""
    x: float
    y: float
    z: float
    size: float
    neighbors: List['GridCell'] = None
    
    def __post_init__(self):
        if self.neighbors is None:
            self.neighbors = []


@dataclass
class Surface3D:
    """三维地表"""
    triangles: List[Triangle]
    bounds: BoundingBox3D
    point_count: int
    area: float = 0.0
    
    def __post_init__(self):
        if self.area == 0.0:
            self.area = sum(triangle.area() for triangle in self.triangles)


class TINGenerator:
    """TIN（不规则三角网）生成器"""
    
    def __init__(self):
        self.points = []
        self.triangles = []
    
    def generate_tin(self, points: List[TerrainPoint]) -> Surface3D:
        """生成TIN"""
        if len(points) < 3:
            raise ValueError("至少需要3个点才能生成TIN")
        
        self.points = points
        
        # 使用简化的Delaunay三角剖分算法
        triangles = self._delaunay_triangulation(points)
        
        # 计算边界
        bounds = self._calculate_bounds(points)
        
        return Surface3D(
            triangles=triangles,
            bounds=bounds,
            point_count=len(points)
        )
    
    def _delaunay_triangulation(self, points: List[TerrainPoint]) -> List[Triangle]:
        """简化的Delaunay三角剖分"""
        if len(points) < 3:
            return []
        
        triangles = []
        
        # 对于小数据集，使用简单的三角剖分
        if len(points) <= 100:
            triangles = self._simple_triangulation(points)
        else:
            # 对于大数据集，使用分治法
            triangles = self._divide_and_conquer_triangulation(points)
        
        return triangles
    
    def _simple_triangulation(self, points: List[TerrainPoint]) -> List[Triangle]:
        """简单三角剖分（适用于小数据集）"""
        triangles = []
        n = len(points)
        
        # 找到凸包
        hull_indices = self._convex_hull(points)
        
        # 从凸包开始，逐步添加内部点
        for i in range(len(hull_indices)):
            p1_idx = hull_indices[i]
            p2_idx = hull_indices[(i + 1) % len(hull_indices)]
            
            # 寻找最佳的第三个点
            best_point_idx = -1
            best_score = float('inf')
            
            for j in range(n):
                if j == p1_idx or j == p2_idx:
                    continue
                
                # 计算外接圆半径作为评分标准
                score = self._circumradius(points[p1_idx], points[p2_idx], points[j])
                if score < best_score:
                    best_score = score
                    best_point_idx = j
            
            if best_point_idx >= 0:
                triangle = Triangle(points[p1_idx], points[p2_idx], points[best_point_idx])
                triangles.append(triangle)
        
        # 添加内部三角形（简化处理）
        remaining_points = [i for i in range(n) if i not in hull_indices]
        
        while len(remaining_points) >= 3:
            # 取前三个点组成三角形
            if len(remaining_points) >= 3:
                triangle = Triangle(
                    points[remaining_points[0]],
                    points[remaining_points[1]], 
                    points[remaining_points[2]]
                )
                triangles.append(triangle)
                remaining_points = remaining_points[3:]
        
        return triangles
    
    def _divide_and_conquer_triangulation(self, points: List[TerrainPoint]) -> List[Triangle]:
        """分治法三角剖分"""
        # 简化实现：将点集分成小块，分别三角剖分
        triangles = []
        chunk_size = 50
        
        for i in range(0, len(points), chunk_size):
            chunk = points[i:i + chunk_size]
            if len(chunk) >= 3:
                chunk_triangles = self._simple_triangulation(chunk)
                triangles.extend(chunk_triangles)
        
        return triangles
    
    def _convex_hull(self, points: List[TerrainPoint]) -> List[int]:
        """计算凸包（Graham扫描法）"""
        if len(points) < 3:
            return list(range(len(points)))
        
        # 找到最下方的点
        start_idx = 0
        for i in range(1, len(points)):
            if (points[i].y < points[start_idx].y or 
                (points[i].y == points[start_idx].y and points[i].x < points[start_idx].x)):
                start_idx = i
        
        # 按极角排序
        def polar_angle(p1, p2):
            dx = p2.x - p1.x
            dy = p2.y - p1.y
            return math.atan2(dy, dx)
        
        start_point = points[start_idx]
        sorted_indices = sorted(
            range(len(points)),
            key=lambda i: (polar_angle(start_point, points[i]), 
                          start_point.distance_to_2d(points[i]))
        )
        
        # Graham扫描
        hull = []
        for idx in sorted_indices:
            while (len(hull) > 1 and 
                   self._cross_product(points[hull[-2]], points[hull[-1]], points[idx]) <= 0):
                hull.pop()
            hull.append(idx)
        
        return hull
    
    def _cross_product(self, p1: TerrainPoint, p2: TerrainPoint, p3: TerrainPoint) -> float:
        """计算叉积"""
        return ((p2.x - p1.x) * (p3.y - p1.y) - (p2.y - p1.y) * (p3.x - p1.x))
    
    def _circumradius(self, p1: TerrainPoint, p2: TerrainPoint, p3: TerrainPoint) -> float:
        """计算外接圆半径"""
        a = p1.distance_to_2d(p2)
        b = p2.distance_to_2d(p3)
        c = p3.distance_to_2d(p1)
        
        area = abs(self._cross_product(p1, p2, p3)) / 2.0
        if area < 1e-10:
            return float('inf')
        
        return (a * b * c) / (4.0 * area)
    
    def _calculate_bounds(self, points: List[TerrainPoint]) -> BoundingBox3D:
        """计算边界框"""
        if not points:
            return BoundingBox3D(0, 0, 0, 0, 0, 0)
        
        min_x = min_y = min_z = float('inf')
        max_x = max_y = max_z = float('-inf')
        
        for point in points:
            min_x = min(min_x, point.x)
            max_x = max(max_x, point.x)
            min_y = min(min_y, point.y)
            max_y = max(max_y, point.y)
            min_z = min(min_z, point.z)
            max_z = max(max_z, point.z)
        
        return BoundingBox3D(min_x, min_y, min_z, max_x, max_y, max_z)


class GridSurfaceGenerator:
    """网格地表生成器"""
    
    def __init__(self, grid_size: float = 10.0):
        self.grid_size = grid_size
    
    def generate_grid_surface(self, points: List[TerrainPoint], 
                            interpolation_method: str = 'idw') -> Surface3D:
        """生成网格地表"""
        if not points:
            raise ValueError("需要至少一个点来生成网格地表")
        
        # 计算边界
        bounds = self._calculate_bounds(points)
        
        # 生成网格点
        grid_points = self._generate_grid_points(bounds)
        
        # 插值计算网格点高程
        for grid_point in grid_points:
            if interpolation_method == 'idw':
                grid_point.z = self._idw_interpolation(grid_point, points)
            elif interpolation_method == 'nearest':
                grid_point.z = self._nearest_neighbor_interpolation(grid_point, points)
            else:
                grid_point.z = self._linear_interpolation(grid_point, points)
        
        # 生成三角形
        triangles = self._grid_to_triangles(grid_points, bounds)
        
        return Surface3D(
            triangles=triangles,
            bounds=bounds,
            point_count=len(grid_points)
        )
    
    def _generate_grid_points(self, bounds: BoundingBox3D) -> List[TerrainPoint]:
        """生成网格点"""
        grid_points = []
        
        x = bounds.min_x
        while x <= bounds.max_x:
            y = bounds.min_y
            while y <= bounds.max_y:
                grid_points.append(TerrainPoint(x, y, 0.0))  # 高程稍后插值
                y += self.grid_size
            x += self.grid_size
        
        return grid_points
    
    def _idw_interpolation(self, target_point: TerrainPoint, 
                          source_points: List[TerrainPoint], power: float = 2.0) -> float:
        """反距离权重插值"""
        if not source_points:
            return 0.0
        
        # 检查是否有重合点
        for point in source_points:
            distance = target_point.distance_to_2d(point)
            if distance < 1e-6:
                return point.z
        
        # 计算权重和加权高程
        weighted_sum = 0.0
        weight_sum = 0.0
        
        for point in source_points:
            distance = target_point.distance_to_2d(point)
            if distance > 0:
                weight = 1.0 / (distance ** power)
                weighted_sum += weight * point.z
                weight_sum += weight
        
        if weight_sum > 0:
            return weighted_sum / weight_sum
        else:
            return 0.0
    
    def _nearest_neighbor_interpolation(self, target_point: TerrainPoint,
                                      source_points: List[TerrainPoint]) -> float:
        """最近邻插值"""
        if not source_points:
            return 0.0
        
        nearest_point = min(source_points, 
                          key=lambda p: target_point.distance_to_2d(p))
        return nearest_point.z
    
    def _linear_interpolation(self, target_point: TerrainPoint,
                            source_points: List[TerrainPoint]) -> float:
        """线性插值（简化实现）"""
        # 找到最近的3个点进行三角插值
        if len(source_points) < 3:
            return self._nearest_neighbor_interpolation(target_point, source_points)
        
        # 按距离排序，取最近的3个点
        sorted_points = sorted(source_points, 
                             key=lambda p: target_point.distance_to_2d(p))
        
        # 使用前3个点构成三角形进行插值
        triangle = Triangle(sorted_points[0], sorted_points[1], sorted_points[2])
        
        if triangle.contains_point(target_point.x, target_point.y):
            return triangle.interpolate_elevation(target_point.x, target_point.y)
        else:
            # 如果不在三角形内，使用IDW插值
            return self._idw_interpolation(target_point, sorted_points[:3])
    
    def _grid_to_triangles(self, grid_points: List[TerrainPoint], 
                          bounds: BoundingBox3D) -> List[Triangle]:
        """将网格点转换为三角形"""
        triangles = []
        
        # 计算网格尺寸
        cols = int((bounds.max_x - bounds.min_x) / self.grid_size) + 1
        rows = int((bounds.max_y - bounds.min_y) / self.grid_size) + 1
        
        # 为每个网格单元生成两个三角形
        for i in range(rows - 1):
            for j in range(cols - 1):
                # 获取四个角点的索引
                idx1 = i * cols + j
                idx2 = i * cols + j + 1
                idx3 = (i + 1) * cols + j
                idx4 = (i + 1) * cols + j + 1
                
                # 检查索引有效性
                if (idx1 < len(grid_points) and idx2 < len(grid_points) and 
                    idx3 < len(grid_points) and idx4 < len(grid_points)):
                    
                    # 生成两个三角形
                    triangle1 = Triangle(grid_points[idx1], grid_points[idx2], grid_points[idx3])
                    triangle2 = Triangle(grid_points[idx2], grid_points[idx4], grid_points[idx3])
                    
                    triangles.append(triangle1)
                    triangles.append(triangle2)
        
        return triangles
    
    def _calculate_bounds(self, points: List[TerrainPoint]) -> BoundingBox3D:
        """计算边界框"""
        if not points:
            return BoundingBox3D(0, 0, 0, 0, 0, 0)
        
        min_x = min_y = min_z = float('inf')
        max_x = max_y = max_z = float('-inf')
        
        for point in points:
            min_x = min(min_x, point.x)
            max_x = max(max_x, point.x)
            min_y = min(min_y, point.y)
            max_y = max(max_y, point.y)
            min_z = min(min_z, point.z)
            max_z = max(max_z, point.z)
        
        return BoundingBox3D(min_x, min_y, min_z, max_x, max_y, max_z)


class SurfaceAnalyzer:
    """地表分析器"""
    
    @staticmethod
    def calculate_slope(surface: Surface3D, x: float, y: float, 
                       sample_distance: float = 1.0) -> Tuple[float, float]:
        """计算指定点的坡度和坡向"""
        # 在指定点周围采样
        points = [
            (x + sample_distance, y),
            (x - sample_distance, y),
            (x, y + sample_distance),
            (x, y - sample_distance)
        ]
        
        elevations = []
        for px, py in points:
            elevation = SurfaceAnalyzer.interpolate_elevation(surface, px, py)
            elevations.append(elevation)
        
        # 计算梯度
        dx = (elevations[0] - elevations[1]) / (2 * sample_distance)
        dy = (elevations[2] - elevations[3]) / (2 * sample_distance)
        
        # 计算坡度（度）
        slope = math.degrees(math.atan(math.sqrt(dx*dx + dy*dy)))
        
        # 计算坡向（度，北为0度）
        aspect = math.degrees(math.atan2(dy, dx))
        if aspect < 0:
            aspect += 360
        
        return slope, aspect
    
    @staticmethod
    def interpolate_elevation(surface: Surface3D, x: float, y: float) -> float:
        """在地表上插值高程"""
        # 查找包含该点的三角形
        for triangle in surface.triangles:
            if triangle.contains_point(x, y):
                return triangle.interpolate_elevation(x, y)
        
        # 如果没有找到包含的三角形，使用最近的三角形
        if surface.triangles:
            nearest_triangle = min(surface.triangles,
                                 key=lambda t: min(
                                     math.sqrt((t.p1.x - x)**2 + (t.p1.y - y)**2),
                                     math.sqrt((t.p2.x - x)**2 + (t.p2.y - y)**2),
                                     math.sqrt((t.p3.x - x)**2 + (t.p3.y - y)**2)
                                 ))
            return nearest_triangle.interpolate_elevation(x, y)
        
        return 0.0
    
    @staticmethod
    def calculate_volume(surface: Surface3D, base_elevation: float = 0.0) -> float:
        """计算地表体积"""
        total_volume = 0.0
        
        for triangle in surface.triangles:
            # 计算三角形的平均高程
            avg_elevation = (triangle.p1.z + triangle.p2.z + triangle.p3.z) / 3.0
            
            # 计算体积贡献
            height = avg_elevation - base_elevation
            area = triangle.area()
            volume = area * height
            
            total_volume += volume
        
        return total_volume
    
    @staticmethod
    def find_contour_lines(surface: Surface3D, elevation: float) -> List[List[Tuple[float, float]]]:
        """查找等高线"""
        contour_lines = []
        
        for triangle in surface.triangles:
            # 检查三角形是否与等高线相交
            intersections = []
            
            edges = [
                (triangle.p1, triangle.p2),
                (triangle.p2, triangle.p3),
                (triangle.p3, triangle.p1)
            ]
            
            for p1, p2 in edges:
                if ((p1.z <= elevation <= p2.z) or (p2.z <= elevation <= p1.z)) and p1.z != p2.z:
                    # 线性插值找到交点
                    t = (elevation - p1.z) / (p2.z - p1.z)
                    x = p1.x + t * (p2.x - p1.x)
                    y = p1.y + t * (p2.y - p1.y)
                    intersections.append((x, y))
            
            if len(intersections) >= 2:
                contour_lines.append(intersections)
        
        return contour_lines


# 扩展TerrainPoint类以支持2D距离计算
def distance_to_2d(self, other: 'TerrainPoint') -> float:
    """计算到另一点的2D距离"""
    return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)

# 动态添加方法
TerrainPoint.distance_to_2d = distance_to_2d


# 工厂函数
def create_tin_generator() -> TINGenerator:
    """创建TIN生成器"""
    return TINGenerator()


def create_grid_surface_generator(grid_size: float = 10.0) -> GridSurfaceGenerator:
    """创建网格地表生成器"""
    return GridSurfaceGenerator(grid_size)


def generate_surface_from_points(points: List[TerrainPoint], 
                               method: str = 'tin') -> Surface3D:
    """从点生成地表的便捷函数"""
    if method == 'tin':
        generator = create_tin_generator()
        return generator.generate_tin(points)
    elif method == 'grid':
        generator = create_grid_surface_generator()
        return generator.generate_grid_surface(points)
    else:
        raise ValueError(f"不支持的地表生成方法: {method}")


def create_sample_terrain_points(grid_size: int = 5) -> List[TerrainPoint]:
    """创建示例地形点"""
    points = []
    
    for i in range(grid_size):
        for j in range(grid_size):
            x = i * 10.0
            y = j * 10.0
            z = 100.0 + math.sin(i * 0.5) * 5 + math.cos(j * 0.5) * 3
            points.append(TerrainPoint(x, y, z))
    
    return points