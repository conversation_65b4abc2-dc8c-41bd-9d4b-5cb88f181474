version: '3.8'

services:
  # 主应用服务
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=***************************************/mining_roads
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped

  # PostgreSQL数据库
  db:
    image: postgis/postgis:15-3.3
    environment:
      - POSTGRES_DB=mining_roads
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=mining123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Celery工作进程
  celery:
    build: .
    command: celery -A src.core.celery worker --loglevel=info
    environment:
      - DATABASE_URL=***************************************/mining_roads
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped

  # Celery监控
  flower:
    build: .
    command: celery -A src.core.celery flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - redis
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data: