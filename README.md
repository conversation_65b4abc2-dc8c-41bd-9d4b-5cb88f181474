# 露天矿山采矿工程道路设计软件

## 项目简介

露天矿山采矿工程道路设计软件是一个基于Python Web应用的专业道路设计系统，集成Cesium GIS组件，严格遵循露天矿山道路设计标准。

## 主要功能

- 🛣️ **道路选线功能** - 智能道路选线和设计
- 🔍 **冲突检测功能** - 自动检测设计冲突
- 🛡️ **安全检测功能** - 安全参数验证和分析
- 📏 **道路剖切功能** - 详细的断面分析
- 🚛 **运输路线优化** - 多目标路线优化
- 🌍 **三维GIS可视化** - 基于Cesium的三维展示
- 📁 **CAD数据处理** - AutoCAD文件导入导出
- 🎨 **现代化界面** - 简约风格用户界面

## 技术架构

### 后端技术栈
- **FastAPI** - 高性能Web框架
- **PostgreSQL + PostGIS** - 地理空间数据库
- **SQLAlchemy** - ORM框架
- **Celery + Redis** - 异步任务队列
- **NumPy/SciPy** - 科学计算

### 前端技术栈
- **React** - 现代化前端框架
- **Cesium.js** - 三维地球引擎
- **Ant Design** - UI组件库
- **WebGL** - 硬件加速渲染

## 项目结构

```
├── src/                    # 源代码目录
│   ├── core/              # 核心模块
│   │   ├── config.py      # 配置管理
│   │   ├── database.py    # 数据库连接
│   │   └── celery.py      # 任务队列
│   ├── api/               # API接口
│   │   └── v1/            # API v1版本
│   ├── tasks/             # 异步任务
│   └── main.py            # 应用入口
├── tests/                 # 测试目录
├── uploads/               # 文件上传目录
├── logs/                  # 日志目录
├── config/                # 配置文件目录
├── requirements.txt       # Python依赖
├── Dockerfile            # Docker配置
├── docker-compose.yml    # Docker编排
└── README.md             # 项目说明
```

## 快速开始

### 方式一：直接运行

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **配置环境**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件配置数据库等参数
   ```

3. **启动应用**
   ```bash
   python -m uvicorn src.main:app --reload
   ```

4. **访问应用**
   - 应用地址: http://localhost:8000
   - API文档: http://localhost:8000/docs

### 方式二：Docker运行

1. **启动服务**
   ```bash
   docker-compose up -d
   ```

2. **访问应用**
   - 应用地址: http://localhost:8000
   - 任务监控: http://localhost:5555

### Windows快速启动

双击运行 `run.bat` 文件即可自动安装依赖并启动应用。

## 开发指南

### 环境要求

- Python 3.11+
- PostgreSQL 13+ (带PostGIS扩展)
- Redis 6+
- Node.js 18+ (前端开发)

### 开发工具

- **代码格式化**: Black
- **代码检查**: Flake8
- **测试框架**: Pytest
- **API文档**: FastAPI自动生成

### 运行测试

```bash
# 运行所有测试
python -m pytest

# 运行特定测试
python -m pytest tests/test_main.py -v

# 基础架构测试
python test_basic.py
```

## 配置说明

主要配置项在 `.env` 文件中：

```env
# 应用配置
APP_NAME=露天矿山道路设计软件
DEBUG=true
PORT=8000

# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost/mining_roads

# Redis配置
REDIS_URL=redis://localhost:6379/0

# Cesium配置
CESIUM_ION_TOKEN=your-token-here
```

## API文档

启动应用后访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。