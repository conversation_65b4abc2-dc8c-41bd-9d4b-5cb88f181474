"""
快速功能测试
Quick Function Test
"""

import sys
import os

def test_python_environment():
    """测试Python环境"""
    print("🔍 Python环境检查:")
    print(f"   版本: {sys.version}")
    print(f"   路径: {sys.executable}")
    return True

def test_basic_imports():
    """测试基础模块导入"""
    print("\n🔍 基础模块导入测试:")
    
    try:
        import fastapi
        print(f"   ✅ FastAPI: {fastapi.__version__}")
    except ImportError as e:
        print(f"   ❌ FastAPI导入失败: {e}")
        return False
    
    try:
        import uvicorn
        print(f"   ✅ Uvicorn: {uvicorn.__version__}")
    except ImportError as e:
        print(f"   ❌ Uvicorn导入失败: {e}")
        return False
    
    try:
        import sqlalchemy
        print(f"   ✅ SQLAlchemy: {sqlalchemy.__version__}")
    except ImportError as e:
        print(f"   ❌ SQLAlchemy导入失败: {e}")
        return False
    
    try:
        import pydantic
        print(f"   ✅ Pydantic: {pydantic.__version__}")
    except ImportError as e:
        print(f"   ❌ Pydantic导入失败: {e}")
        return False
    
    return True

def test_project_modules():
    """测试项目模块"""
    print("\n🔍 项目模块导入测试:")
    
    # 添加项目路径
    sys.path.insert(0, os.getcwd())
    
    try:
        from src.core.config import settings
        print(f"   ✅ 配置模块: {settings.APP_NAME}")
    except ImportError as e:
        print(f"   ❌ 配置模块导入失败: {e}")
        return False
    
    try:
        from src.core.database import engine, Base
        print("   ✅ 数据库模块导入成功")
    except ImportError as e:
        print(f"   ❌ 数据库模块导入失败: {e}")
        return False
    
    try:
        from src.api.v1.api import api_router
        print("   ✅ API路由模块导入成功")
    except ImportError as e:
        print(f"   ❌ API路由模块导入失败: {e}")
        return False
    
    try:
        from src.main import app
        print("   ✅ 主应用模块导入成功")
    except ImportError as e:
        print(f"   ❌ 主应用模块导入失败: {e}")
        return False
    
    return True

def test_environment_config():
    """测试环境配置"""
    print("\n🔍 环境配置测试:")
    
    if os.path.exists(".env"):
        print("   ✅ .env 文件存在")
    else:
        print("   ❌ .env 文件不存在")
        return False
    
    required_dirs = ["uploads", "logs", "config", "src", "tests"]
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"   ✅ 目录存在: {dir_name}")
        else:
            print(f"   ❌ 目录缺失: {dir_name}")
            return False
    
    return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("露天矿山道路设计软件 - 快速功能测试")
    print("Mining Road Design Software - Quick Function Test")
    print("=" * 60)
    
    success = True
    success &= test_python_environment()
    success &= test_basic_imports()
    success &= test_project_modules()
    success &= test_environment_config()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！项目可以正常运行")
        print("\n下一步:")
        print("1. 运行 run.bat (Windows) 或 ./run.sh (Linux/Mac)")
        print("2. 访问 http://localhost:8000")
        print("3. 查看API文档 http://localhost:8000/docs")
    else:
        print("❌ 部分测试失败，请检查配置和依赖")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
