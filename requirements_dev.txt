# 开发环境依赖 - 最小化配置
# Development Dependencies - Minimal Configuration

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库 - SQLite支持
sqlalchemy==2.0.23
alembic==1.12.1

# 配置管理
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# 基础科学计算
numpy==1.25.2

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1

# 其他必需工具
python-multipart==0.0.6
jinja2==3.1.2

# 可选依赖（生产环境需要）
# psycopg2-binary==2.9.9  # PostgreSQL
# redis==5.0.1            # Redis缓存
# celery==5.3.4           # 任务队列
# geoalchemy2==0.14.2     # GIS支持
# shapely==2.0.2          # 几何计算
# pyproj==3.6.1           # 坐标转换
# ezdxf==1.1.4            # CAD文件
# scipy==1.11.4           # 科学计算
# pandas==2.1.3           # 数据处理
