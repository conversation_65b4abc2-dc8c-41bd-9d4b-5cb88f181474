"""
应用配置管理
Application Configuration Management
"""

try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    APP_NAME: str = "露天矿山道路设计软件"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://postgres:password@localhost:5432/mining_roads"
    DATABASE_ECHO: bool = False
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # GIS配置
    GDAL_DATA_PATH: Optional[str] = None
    PROJ_LIB_PATH: Optional[str] = None
    
    # 文件存储配置
    UPLOAD_PATH: str = "./uploads"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_FILE_EXTENSIONS: List[str] = [
        ".dxf", ".dwg", ".shp", ".tif", ".dem", ".xyz", ".las", ".ply"
    ]
    
    # Cesium配置
    CESIUM_ION_TOKEN: Optional[str] = None
    
    # 任务队列配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"
    
    # 道路设计标准配置
    DESIGN_STANDARDS_PATH: str = "./config/design_standards"
    DEFAULT_DESIGN_STANDARD: str = "mining_road_standard_v1.0"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


def get_database_url() -> str:
    """获取数据库连接URL"""
    return settings.DATABASE_URL


def get_redis_url() -> str:
    """获取Redis连接URL"""
    return settings.REDIS_URL


def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.UPLOAD_PATH,
        os.path.dirname(settings.LOG_FILE),
        settings.DESIGN_STANDARDS_PATH,
        "./static",
        "./temp"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


# 初始化时创建目录
ensure_directories()