"""
应用配置管理
Application Configuration Management
"""

try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings
    except ImportError:
        # 如果pydantic不可用，使用简单的配置类
        BaseSettings = object

from typing import List, Optional
import os


if BaseSettings != object:
    # 如果pydantic可用，使用完整的配置类
    class Settings(BaseSettings):
        """应用配置类"""

        # 基础配置
        APP_NAME: str = "露天矿山道路设计软件"
        VERSION: str = "1.0.0"
        DEBUG: bool = False
        HOST: str = "0.0.0.0"
        PORT: int = 8000

        # 安全配置
        SECRET_KEY: str = "your-secret-key-change-in-production"
        ALLOWED_HOSTS: List[str] = ["*"]

        # 数据库配置
        DATABASE_URL: str = "sqlite:///./mining_roads.db"
        DATABASE_ECHO: bool = False

        # Redis配置
        REDIS_URL: str = "redis://localhost:6379/0"

        # GIS配置
        GDAL_DATA_PATH: Optional[str] = None
        PROJ_LIB_PATH: Optional[str] = None

        # 文件存储配置
        UPLOAD_PATH: str = "./uploads"
        MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
        ALLOWED_FILE_EXTENSIONS: List[str] = [
            ".dxf", ".dwg", ".shp", ".tif", ".dem", ".xyz", ".las", ".ply"
        ]

        # Cesium配置
        CESIUM_ION_TOKEN: Optional[str] = None

        # 任务队列配置
        CELERY_BROKER_URL: str = "redis://localhost:6379/1"
        CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"

        # 日志配置
        LOG_LEVEL: str = "INFO"
        LOG_FILE: str = "./logs/app.log"

        # 道路设计标准配置
        DESIGN_STANDARDS_PATH: str = "./config/design_standards"
        DEFAULT_DESIGN_STANDARD: str = "mining_road_standard_v1.0"

        class Config:
            env_file = ".env"
            env_file_encoding = "utf-8"
            case_sensitive = True
else:
    # 如果pydantic不可用，使用简单的配置类
    class Settings:
        """简化的应用配置类"""

        def __init__(self):
            # 基础配置
            self.APP_NAME = os.getenv("APP_NAME", "露天矿山道路设计软件")
            self.VERSION = os.getenv("VERSION", "1.0.0")
            self.DEBUG = os.getenv("DEBUG", "true").lower() == "true"
            self.HOST = os.getenv("HOST", "0.0.0.0")
            self.PORT = int(os.getenv("PORT", "8000"))

            # 安全配置
            self.SECRET_KEY = os.getenv("SECRET_KEY", "dev-secret-key")
            self.ALLOWED_HOSTS = ["*"]

            # 数据库配置
            self.DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./mining_roads.db")
            self.DATABASE_ECHO = os.getenv("DATABASE_ECHO", "false").lower() == "true"

            # 文件存储配置
            self.UPLOAD_PATH = os.getenv("UPLOAD_PATH", "./uploads")
            self.MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "104857600"))

            # 日志配置
            self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
            self.LOG_FILE = os.getenv("LOG_FILE", "./logs/app.log")

            # 道路设计标准配置
            self.DESIGN_STANDARDS_PATH = os.getenv("DESIGN_STANDARDS_PATH", "./config/design_standards")
            self.DEFAULT_DESIGN_STANDARD = os.getenv("DEFAULT_DESIGN_STANDARD", "mining_road_standard_v1.0")


# 创建全局配置实例
settings = Settings()


def get_database_url() -> str:
    """获取数据库连接URL"""
    return settings.DATABASE_URL


def get_redis_url() -> str:
    """获取Redis连接URL"""
    return settings.REDIS_URL


def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.UPLOAD_PATH,
        os.path.dirname(settings.LOG_FILE),
        settings.DESIGN_STANDARDS_PATH,
        "./static",
        "./temp"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


# 初始化时创建目录
ensure_directories()