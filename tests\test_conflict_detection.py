"""
冲突检测和安全分析系统测试
Conflict Detection and Safety Analysis System Tests
"""

import sys
import os
import math

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.conflict_detection import (
    ConflictDetector, SafetyAnalyzer, ComprehensiveAnalyzer,
    create_conflict_detector, create_safety_analyzer, create_comprehensive_analyzer,
    detect_road_conflicts, analyze_road_safety, comprehensive_road_analysis,
    ConflictType, ConflictSeverity, GeometricConflict, SafetyIssue
)
from src.core.design_standards import RoadClass, create_mining_road_standards
from src.services.surface_modeling import create_sample_terrain_points, create_tin_generator


def test_conflict_detector():
    """测试冲突检测器"""
    print("🔍 测试冲突检测器...")
    
    detector = create_conflict_detector()
    
    # 创建测试道路设计数据
    road_design = {
        'alignment': {
            'road_class': RoadClass.MAIN_HAUL,
            'design_speed': 40,
            'horizontal_elements': [
                {
                    'type': 'circular',
                    'start_station': 0,
                    'radius': 30.0,  # 小于标准要求60m
                    'center': [50, 50],
                    'length': 15.0   # 小于最小长度30m
                },
                {
                    'type': 'straight',
                    'start_station': 100,
                    'start_point': [0, 0],
                    'end_point': [100, 0],
                    'length': 100
                }
            ],
            'vertical_elements': [
                {
                    'type': 'grade',
                    'start_station': 0,
                    'grade': 12.0,  # 超过最大坡度8%
                    'length': 200
                },
                {
                    'type': 'vertical_curve',
                    'pvi_station': 200,
                    'radius': 200,  # 小于最小半径400m
                    'curve_type': 'crest'
                }
            ]
        },
        'cross_sections': [
            {
                'station': 0,
                'road_width': 5.0,  # 小于最小宽度6m
                'cut_slope_ratio': 0.8,  # 过陡
                'fill_slope_ratio': 1.0   # 过陡
            }
        ],
        'facilities': [
            {
                'type': 'passing_bay',
                'station': 100
            },
            {
                'type': 'passing_bay',
                'station': 150  # 间距过近
            }
        ]
    }
    
    # 检测冲突
    conflicts = detector.detect_geometric_conflicts(road_design)
    
    assert len(conflicts) > 0, "应该检测到冲突"
    print(f"✅ 检测到 {len(conflicts)} 个冲突")
    
    # 验证冲突类型
    conflict_types = set(c.conflict_type for c in conflicts)
    assert ConflictType.GEOMETRIC in conflict_types, "应该检测到几何冲突"
    print(f"✅ 冲突类型: {', '.join(conflict_types)}")
    
    # 验证严重程度
    severity_counts = {}
    for conflict in conflicts:
        severity = conflict.severity
        severity_counts[severity] = severity_counts.get(severity, 0) + 1
    
    print(f"✅ 严重程度统计: {severity_counts}")
    
    # 检查具体冲突
    radius_conflicts = [c for c in conflicts if '半径' in c.description or 'radius' in c.description.lower()]
    print(f"✅ 半径相关冲突: {len(radius_conflicts)} 个")
    
    grade_conflicts = [c for c in conflicts if '坡度' in c.description or 'grade' in c.description.lower()]
    print(f"✅ 坡度相关冲突: {len(grade_conflicts)} 个")
    
    # 显示所有冲突的描述以便调试
    print("   冲突详情:")
    for i, conflict in enumerate(conflicts[:3]):  # 只显示前3个
        print(f"   {i+1}. {conflict.description} (严重程度: {conflict.severity})")


def test_safety_analyzer():
    """测试安全分析器"""
    print("\n🔍 测试安全分析器...")
    
    analyzer = create_safety_analyzer()
    
    # 创建测试道路设计数据
    road_design = {
        'alignment': {
            'design_speed': 50,
            'horizontal_elements': [
                {
                    'type': 'circular',
                    'start_station': 0,
                    'radius': 40.0,
                    'center': [50, 50],
                    'superelevation': 8.0  # 超过最大超高6%
                }
            ],
            'vertical_elements': [
                {
                    'type': 'vertical_curve',
                    'pvi_station': 100,
                    'radius': 200,  # 可能导致视距不足
                    'curve_type': 'crest'
                }
            ]
        },
        'facilities': [
            {
                'type': 'passing_bay',
                'station': 100,
                'length': 30,  # 小于标准50m
                'width': 3.0   # 小于标准4m
            },
            {
                'type': 'passing_bay',
                'station': 800  # 间距过大
            }
        ],
        'safety_facilities': [
            {
                'type': 'barrier',
                'station': 200,
                'height': 0.8,  # 小于标准1.2m
                'setback': 0.2   # 小于标准0.5m
            }
        ],
        'drainage': {
            'side_ditches': [
                {
                    'station': 0,
                    'depth': 0.2,  # 小于最小0.3m
                    'width': 0.8,
                    'slope': 0.1    # 小于最小0.3%
                }
            ]
        },
        'cross_sections': [
            {
                'station': 0,
                'crown_slope': 1.0  # 小于推荐1.5%
            }
        ]
    }
    
    # 分析安全参数
    safety_issues = analyzer.analyze_safety_parameters(road_design, RoadClass.MAIN_HAUL)
    
    assert len(safety_issues) > 0, "应该检测到安全问题"
    print(f"✅ 检测到 {len(safety_issues)} 个安全问题")
    
    # 验证问题类型
    issue_types = set(i.issue_type for i in safety_issues)
    print(f"✅ 安全问题类型: {', '.join(issue_types)}")
    
    # 验证严重程度
    severity_counts = {}
    for issue in safety_issues:
        severity = issue.severity
        severity_counts[severity] = severity_counts.get(severity, 0) + 1
    
    print(f"✅ 严重程度统计: {severity_counts}")
    
    # 检查具体问题
    superelevation_issues = [i for i in safety_issues if i.issue_type == 'superelevation']
    if superelevation_issues:
        print(f"✅ 超高问题: {len(superelevation_issues)} 个")
    
    passing_bay_issues = [i for i in safety_issues if 'passing_bay' in i.issue_type]
    if passing_bay_issues:
        print(f"✅ 会车道问题: {len(passing_bay_issues)} 个")
    
    barrier_issues = [i for i in safety_issues if 'barrier' in i.issue_type]
    if barrier_issues:
        print(f"✅ 护栏问题: {len(barrier_issues)} 个")


def test_terrain_conflict_detection():
    """测试地形冲突检测"""
    print("\n🔍 测试地形冲突检测...")
    
    # 创建地形数据
    terrain_points = create_sample_terrain_points(grid_size=6)
    tin_generator = create_tin_generator()
    terrain_surface = tin_generator.generate_tin(terrain_points)
    
    # 创建穿越陡峭地形的道路设计
    road_design = {
        'alignment': {
            'horizontal_elements': [
                {
                    'type': 'straight',
                    'start_station': 0,
                    'start_point': [10, 10],
                    'end_point': [40, 40],  # 穿越地形变化较大的区域
                    'length': 42.4
                }
            ]
        }
    }
    
    detector = create_conflict_detector()
    conflicts = detector.detect_geometric_conflicts(road_design, terrain_surface)
    
    print(f"✅ 地形冲突检测完成: {len(conflicts)} 个冲突")
    
    # 检查环境冲突
    env_conflicts = [c for c in conflicts if c.conflict_type == ConflictType.ENVIRONMENTAL]
    if env_conflicts:
        print(f"✅ 环境冲突: {len(env_conflicts)} 个")
        for conflict in env_conflicts:
            print(f"   - {conflict.description}")


def test_comprehensive_analyzer():
    """测试综合分析器"""
    print("\n🔍 测试综合分析器...")
    
    analyzer = create_comprehensive_analyzer()
    
    # 创建复杂的道路设计数据
    road_design = {
        'alignment': {
            'road_class': RoadClass.MAIN_HAUL,
            'design_speed': 40,
            'horizontal_elements': [
                {
                    'type': 'circular',
                    'start_station': 0,
                    'radius': 45.0,  # 小于标准
                    'center': [50, 50],
                    'superelevation': 7.0  # 超过最大值
                }
            ],
            'vertical_elements': [
                {
                    'type': 'grade',
                    'start_station': 0,
                    'grade': 9.0,  # 超过最大坡度
                    'length': 150
                }
            ]
        },
        'cross_sections': [
            {
                'station': 0,
                'road_width': 5.5,  # 小于标准
                'crown_slope': 1.0   # 小于推荐值
            }
        ],
        'facilities': [
            {
                'type': 'passing_bay',
                'station': 100,
                'length': 35,  # 小于标准
                'width': 3.5   # 小于标准
            }
        ]
    }
    
    # 综合分析
    report = analyzer.comprehensive_analysis(road_design, road_class=RoadClass.MAIN_HAUL)
    
    assert 'analysis_summary' in report, "应该包含分析摘要"
    assert 'geometric_conflicts' in report, "应该包含几何冲突"
    assert 'safety_issues' in report, "应该包含安全问题"
    assert 'recommendations' in report, "应该包含建议"
    
    summary = report['analysis_summary']
    print(f"✅ 综合分析完成:")
    print(f"   总冲突数: {summary['total_conflicts']}")
    print(f"   总安全问题: {summary['total_safety_issues']}")
    print(f"   严重问题: {summary['critical_issues']}")
    print(f"   高级问题: {summary['high_issues']}")
    print(f"   中级问题: {summary['medium_issues']}")
    print(f"   低级问题: {summary['low_issues']}")
    
    # 验证建议
    recommendations = report['recommendations']
    assert len(recommendations) > 0, "应该有改进建议"
    print(f"✅ 改进建议: {len(recommendations)} 条")
    for i, rec in enumerate(recommendations, 1):
        print(f"   {i}. {rec}")


def test_conflict_location():
    """测试冲突位置信息"""
    print("\n🔍 测试冲突位置信息...")
    
    road_design = {
        'alignment': {
            'horizontal_elements': [
                {
                    'type': 'circular',
                    'start_station': 150,
                    'radius': 35.0,
                    'center': [100, 200],
                    'length': 20
                }
            ]
        }
    }
    
    conflicts = detect_road_conflicts(road_design)
    
    for conflict in conflicts:
        location = conflict.location
        print(f"✅ 冲突位置:")
        print(f"   桩号: K{location.station/1000:.3f}")
        print(f"   坐标: ({location.x:.1f}, {location.y:.1f})")
        print(f"   描述: {location.description}")
        print(f"   冲突描述: {conflict.description}")
        print(f"   严重程度: {conflict.severity}")
        print(f"   建议措施: {', '.join(conflict.recommendations[:2])}")
        break  # 只显示第一个冲突的详细信息


def test_safety_parameter_calculation():
    """测试安全参数计算"""
    print("\n🔍 测试安全参数计算...")
    
    road_design = {
        'alignment': {
            'design_speed': 60,
            'horizontal_elements': [
                {
                    'type': 'circular',
                    'start_station': 0,
                    'radius': 80.0,
                    'center': [100, 100],
                    'superelevation': 4.0
                }
            ]
        }
    }
    
    safety_issues = analyze_road_safety(road_design, RoadClass.MAIN_HAUL)
    
    for issue in safety_issues:
        if issue.issue_type == 'superelevation':
            print(f"✅ 超高分析:")
            print(f"   标准值: {issue.standard_value:.1f}%")
            print(f"   实际值: {issue.actual_value:.1f}%")
            print(f"   偏差: {issue.deviation:.1f}%")
            print(f"   建议: {', '.join(issue.recommendations[:2])}")
            break


def test_design_standards_integration():
    """测试设计标准集成"""
    print("\n🔍 测试设计标准集成...")
    
    # 使用不同道路等级
    road_classes = [RoadClass.MAIN_HAUL, RoadClass.SECONDARY_HAUL, RoadClass.RAMP_ROAD]
    
    road_design = {
        'alignment': {
            'horizontal_elements': [
                {
                    'type': 'circular',
                    'start_station': 0,
                    'radius': 50.0,
                    'center': [50, 50]
                }
            ]
        }
    }
    
    for road_class in road_classes:
        road_design['alignment']['road_class'] = road_class
        conflicts = detect_road_conflicts(road_design)
        
        print(f"✅ {road_class} 冲突检测: {len(conflicts)} 个冲突")
        
        # 不同道路等级应该有不同的标准要求
        if conflicts:
            print(f"   示例冲突: {conflicts[0].description}")


def run_all_tests():
    """运行所有测试"""
    print("=" * 70)
    print("冲突检测和安全分析系统测试")
    print("Conflict Detection and Safety Analysis System Tests")
    print("=" * 70)
    
    try:
        test_conflict_detector()
        test_safety_analyzer()
        test_terrain_conflict_detection()
        test_comprehensive_analyzer()
        test_conflict_location()
        test_safety_parameter_calculation()
        test_design_standards_integration()
        
        print("\n" + "=" * 70)
        print("🎉 所有冲突检测和安全分析测试通过！")
        print("✅ 冲突检测和安全分析系统功能正常")
        print("=" * 70)
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        print("=" * 70)
        return False


if __name__ == "__main__":
    run_all_tests()