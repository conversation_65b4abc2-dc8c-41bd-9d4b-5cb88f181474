"""
基础数据模型
Base Data Models
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4

try:
    from sqlalchemy import Column, String, DateTime, Boolean
    from sqlalchemy.dialects.postgresql import UUID as PGUUID
    from sqlalchemy.ext.declarative import declarative_base
    from pydantic import BaseModel, Field
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    from pydantic import BaseModel, Field

# Pydantic基础模型
class BaseSchema(BaseModel):
    """Pydantic基础模式"""
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class TimestampMixin(BaseSchema):
    """时间戳混入"""
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None


class UUIDMixin(BaseSchema):
    """UUID混入"""
    id: UUID = Field(default_factory=uuid4)


# SQLAlchemy基础模型（如果可用）
if SQLALCHEMY_AVAILABLE:
    Base = declarative_base()
    
    class SQLAlchemyBase(Base):
        """SQLAlchemy基础模型"""
        __abstract__ = True
        
        id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
        created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
        updated_at = Column(DateTime, onupdate=datetime.utcnow)
        is_active = Column(Boolean, default=True, nullable=False)
        
        def to_dict(self):
            """转换为字典"""
            return {c.name: getattr(self, c.name) for c in self.__table__.columns}
else:
    Base = None
    SQLAlchemyBase = None


# 枚举类型
class DesignStatus(str):
    """设计状态枚举"""
    DRAFT = "draft"
    IN_PROGRESS = "in_progress"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    COMPLETED = "completed"


class GeometryType(str):
    """几何类型枚举"""
    POINT = "point"
    LINE = "line"
    POLYGON = "polygon"
    MULTIPOINT = "multipoint"
    MULTILINE = "multiline"
    MULTIPOLYGON = "multipolygon"


class CoordinateSystem(str):
    """坐标系统枚举"""
    WGS84 = "EPSG:4326"
    WEB_MERCATOR = "EPSG:3857"
    UTM_ZONE_50N = "EPSG:32650"
    CGCS2000 = "EPSG:4490"