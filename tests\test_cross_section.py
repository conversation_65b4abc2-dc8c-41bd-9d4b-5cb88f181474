"""
横断面设计模块测试
Cross Section Design Module Tests
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.cross_section import (
    CrossSectionTemplate, CrossSectionDesigner, CrossSectionAnalyzer,
    GroundPoint, DesignPoint, CrossSectionData,
    create_mining_road_template, create_cross_section_designer,
    create_sample_ground_points, create_sample_cross_section
)


def test_cross_section_template():
    """测试横断面模板"""
    print("🔍 测试横断面模板...")
    
    template = create_mining_road_template()
    
    assert template.name == "露天矿山道路标准断面"
    assert template.road_width == 7.0
    assert template.shoulder_width == 1.0
    assert template.cut_slope_ratio == 1.5
    
    print(f"✅ 模板创建成功: {template.name}")
    print(f"   路面宽度: {template.road_width}m")
    print(f"   路肩宽度: {template.shoulder_width}m")
    print(f"   边坡坡率: 1:{template.cut_slope_ratio}")


def test_ground_points():
    """测试地面点"""
    print("\n🔍 测试地面点...")
    
    ground_points = create_sample_ground_points()
    
    assert len(ground_points) == 7
    assert ground_points[0].offset == -20.0
    assert ground_points[-1].offset == 20.0
    
    print(f"✅ 地面点创建成功: {len(ground_points)} 个点")
    
    # 验证地面点排序
    offsets = [p.offset for p in ground_points]
    assert offsets == sorted(offsets), "地面点未按偏距排序"
    print("✅ 地面点排序正确")


def test_cross_section_designer():
    """测试横断面设计器"""
    print("\n🔍 测试横断面设计器...")
    
    designer = create_cross_section_designer("mining")
    
    # 测试设计点生成
    centerline_elevation = 101.0
    design_points = designer._generate_design_points(centerline_elevation)
    
    assert len(design_points) >= 5, f"设计点数量不足: {len(design_points)}"
    print(f"✅ 设计点生成成功: {len(design_points)} 个点")
    
    # 验证中线点
    centerline_point = next((p for p in design_points if p.point_type == "centerline"), None)
    assert centerline_point is not None, "未找到中线点"
    assert centerline_point.offset == 0.0, "中线点偏距错误"
    assert centerline_point.elevation == centerline_elevation, "中线点高程错误"
    print("✅ 中线点正确")
    
    # 验证路面边缘点
    edge_points = [p for p in design_points if p.point_type == "edge"]
    assert len(edge_points) == 2, f"路面边缘点数量错误: {len(edge_points)}"
    
    left_edge = next((p for p in edge_points if p.offset < 0), None)
    right_edge = next((p for p in edge_points if p.offset > 0), None)
    
    assert left_edge is not None and right_edge is not None, "路面边缘点缺失"
    assert abs(left_edge.offset) == abs(right_edge.offset), "路面边缘点不对称"
    print("✅ 路面边缘点正确")


def test_cross_section_design():
    """测试横断面设计"""
    print("\n🔍 测试横断面设计...")
    
    cross_section = create_sample_cross_section()
    
    assert cross_section.station == 100.0, "桩号错误"
    assert cross_section.centerline_elevation == 101.0, "中线高程错误"
    assert len(cross_section.ground_points) == 7, "地面点数量错误"
    assert len(cross_section.design_points) >= 5, "设计点数量错误"
    
    print(f"✅ 横断面设计成功")
    print(f"   桩号: K0+{cross_section.station:03.0f}")
    print(f"   中线高程: {cross_section.centerline_elevation}m")
    print(f"   挖方面积: {cross_section.cut_area:.2f}m²")
    print(f"   填方面积: {cross_section.fill_area:.2f}m²")


def test_ground_elevation_interpolation():
    """测试地面高程插值"""
    print("\n🔍 测试地面高程插值...")
    
    designer = create_cross_section_designer()
    ground_points = [
        GroundPoint(-10.0, 100.0),
        GroundPoint(0.0, 101.0),
        GroundPoint(10.0, 102.0)
    ]
    
    # 测试中点插值
    elevation = designer._interpolate_ground_elevation(ground_points, 5.0)
    expected_elevation = 101.5  # 线性插值
    assert abs(elevation - expected_elevation) < 0.001, f"插值错误: {elevation}"
    print(f"✅ 地面高程插值正确: {elevation}m")
    
    # 测试边界外插值
    elevation_left = designer._interpolate_ground_elevation(ground_points, -20.0)
    assert elevation_left == 100.0, "左边界插值错误"
    
    elevation_right = designer._interpolate_ground_elevation(ground_points, 20.0)
    assert elevation_right == 102.0, "右边界插值错误"
    print("✅ 边界插值正确")


def test_earthwork_calculation():
    """测试土方量计算"""
    print("\n🔍 测试土方量计算...")
    
    # 创建多个横断面
    cross_sections = []
    for i, station in enumerate([0, 20, 40, 60, 80, 100]):
        designer = create_cross_section_designer("mining")
        ground_points = create_sample_ground_points()
        
        # 调整地面高程以产生不同的挖填情况
        for point in ground_points:
            point.elevation += i * 0.1  # 逐渐抬高地面
        
        cross_section = designer.design_cross_section(
            station=station,
            centerline_elevation=101.0,
            ground_points=ground_points
        )
        cross_sections.append(cross_section)
    
    # 计算土方量
    volumes = CrossSectionAnalyzer.calculate_earthwork_volume(cross_sections)
    
    assert "cut_volume" in volumes, "缺少挖方量"
    assert "fill_volume" in volumes, "缺少填方量"
    assert "net_volume" in volumes, "缺少净土方量"
    
    print(f"✅ 土方量计算成功")
    print(f"   挖方量: {volumes['cut_volume']:.2f}m³")
    print(f"   填方量: {volumes['fill_volume']:.2f}m³")
    print(f"   净土方量: {volumes['net_volume']:.2f}m³")


def test_cross_section_stability():
    """测试横断面稳定性分析"""
    print("\n🔍 测试横断面稳定性分析...")
    
    cross_section = create_sample_cross_section()
    analysis = CrossSectionAnalyzer.analyze_cross_section_stability(cross_section)
    
    assert "stable" in analysis, "缺少稳定性判断"
    assert "warnings" in analysis, "缺少警告信息"
    assert "recommendations" in analysis, "缺少建议信息"
    
    print(f"✅ 稳定性分析完成")
    print(f"   稳定性: {'稳定' if analysis['stable'] else '不稳定'}")
    print(f"   警告数量: {len(analysis['warnings'])}")
    print(f"   建议数量: {len(analysis['recommendations'])}")
    
    if analysis['warnings']:
        for warning in analysis['warnings']:
            print(f"   ⚠️  {warning}")


def test_slope_generation():
    """测试边坡生成"""
    print("\n🔍 测试边坡生成...")
    
    designer = create_cross_section_designer("mining")
    cross_section = create_sample_cross_section()
    
    # 生成边坡点
    slope_points = designer.generate_slope_points(cross_section, max_offset=30.0)
    
    print(f"✅ 边坡点生成: {len(slope_points)} 个点")
    
    # 验证边坡点类型
    slope_point_types = set(p.point_type for p in slope_points)
    assert "slope" in slope_point_types, "未生成边坡点"
    print("✅ 边坡点类型正确")


def test_drainage_ditch():
    """测试排水沟"""
    print("\n🔍 测试排水沟...")
    
    designer = create_cross_section_designer("mining")
    cross_section = create_sample_cross_section()
    
    # 添加排水沟
    ditch_points = designer.add_drainage_ditch(cross_section)
    
    print(f"✅ 排水沟设计: {len(ditch_points)} 个点")
    
    if ditch_points:
        ditch_types = set(p.point_type for p in ditch_points)
        assert "ditch" in ditch_types, "未生成排水沟点"
        print("✅ 排水沟点类型正确")


def run_all_tests():
    """运行所有测试"""
    print("=" * 70)
    print("横断面设计模块测试")
    print("Cross Section Design Module Tests")
    print("=" * 70)
    
    try:
        test_cross_section_template()
        test_ground_points()
        test_cross_section_designer()
        test_cross_section_design()
        test_ground_elevation_interpolation()
        test_earthwork_calculation()
        test_cross_section_stability()
        test_slope_generation()
        test_drainage_ditch()
        
        print("\n" + "=" * 70)
        print("🎉 所有横断面设计测试通过！")
        print("✅ 横断面设计模块功能正常")
        print("=" * 70)
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        print("=" * 70)
        return False


if __name__ == "__main__":
    run_all_tests()