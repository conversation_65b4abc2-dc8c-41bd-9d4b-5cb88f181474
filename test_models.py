"""
简单的模型测试
Simple Model Test
"""

def test_road_design_models():
    """测试道路设计模型"""
    try:
        from src.models.road_design import (
            Point2D, Point3D, RoadDesign, DesignStandards,
            create_default_design_standards, create_sample_road_design
        )
        
        print("✅ 模型导入成功")
        
        # 测试Point2D
        point2d = Point2D(x=100.0, y=200.0)
        print(f"✅ Point2D创建成功: ({point2d.x}, {point2d.y})")
        
        # 测试Point3D
        point3d = Point3D(x=100.0, y=200.0, z=50.0)
        print(f"✅ Point3D创建成功: ({point3d.x}, {point3d.y}, {point3d.z})")
        
        # 测试设计标准
        standards = create_default_design_standards()
        print(f"✅ 设计标准创建成功: {standards.standard_name}")
        
        # 测试道路设计
        design = create_sample_road_design()
        print(f"✅ 道路设计创建成功: {design.name}")
        print(f"   设计ID: {design.id}")
        print(f"   设计状态: {design.status}")
        print(f"   创建时间: {design.created_at}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False


def test_model_validation():
    """测试模型验证"""
    try:
        from src.models.road_design import HorizontalElement, Point2D
        
        # 测试有效的水平线形元素
        element = HorizontalElement(
            element_type="straight",
            start_station=0.0,
            end_station=100.0,
            length=100.0,
            start_point=Point2D(x=0.0, y=0.0),
            end_point=Point2D(x=100.0, y=0.0)
        )
        print(f"✅ 水平线形元素创建成功: {element.element_type}")
        
        # 测试无效的元素类型
        try:
            invalid_element = HorizontalElement(
                element_type="invalid_type",
                start_station=0.0,
                end_station=100.0,
                length=100.0,
                start_point=Point2D(x=0.0, y=0.0),
                end_point=Point2D(x=100.0, y=0.0)
            )
            print("❌ 应该抛出验证错误")
            return False
        except ValueError as e:
            print(f"✅ 验证错误正确捕获: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证测试失败: {e}")
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("道路设计模型测试")
    print("=" * 60)
    
    success = True
    success &= test_road_design_models()
    success &= test_model_validation()
    
    print("=" * 60)
    if success:
        print("🎉 所有模型测试通过！")
    else:
        print("❌ 部分测试失败")
    print("=" * 60)