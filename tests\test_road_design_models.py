"""
道路设计模型测试
Road Design Models Tests
"""

import pytest
from uuid import uuid4
from src.models.road_design import (
    Point2D, Point3D, HorizontalElement, VerticalElement,
    RoadAlignment, CrossSection, RoadDesign, DesignStandards,
    create_default_design_standards, create_sample_road_design
)
from src.models.base import DesignStatus


class TestPoint2D:
    """测试二维点"""
    
    def test_create_point2d(self):
        """测试创建二维点"""
        point = Point2D(x=100.0, y=200.0)
        assert point.x == 100.0
        assert point.y == 200.0
    
    def test_point2d_validation(self):
        """测试二维点验证"""
        with pytest.raises(ValueError):
            Point2D(x="invalid", y=200.0)


class TestPoint3D:
    """测试三维点"""
    
    def test_create_point3d(self):
        """测试创建三维点"""
        point = Point3D(x=100.0, y=200.0, z=50.0)
        assert point.x == 100.0
        assert point.y == 200.0
        assert point.z == 50.0
    
    def test_point3d_default_z(self):
        """测试三维点默认Z值"""
        point = Point3D(x=100.0, y=200.0)
        assert point.z == 0.0


class TestHorizontalElement:
    """测试水平线形元素"""
    
    def test_create_straight_element(self):
        """测试创建直线元素"""
        element = HorizontalElement(
            element_type="straight",
            start_station=0.0,
            end_station=100.0,
            length=100.0,
            start_point=Point2D(x=0.0, y=0.0),
            end_point=Point2D(x=100.0, y=0.0)
        )
        assert element.element_type == "straight"
        assert element.length == 100.0
    
    def test_create_circular_element(self):
        """测试创建圆曲线元素"""
        element = HorizontalElement(
            element_type="circular",
            start_station=100.0,
            end_station=200.0,
            length=100.0,
            start_point=Point2D(x=100.0, y=0.0),
            end_point=Point2D(x=200.0, y=0.0),
            radius=500.0
        )
        assert element.element_type == "circular"
        assert element.radius == 500.0
    
    def test_invalid_element_type(self):
        """测试无效元素类型"""
        with pytest.raises(ValueError):
            HorizontalElement(
                element_type="invalid",
                start_station=0.0,
                end_station=100.0,
                length=100.0,
                start_point=Point2D(x=0.0, y=0.0),
                end_point=Point2D(x=100.0, y=0.0)
            )


class TestVerticalElement:
    """测试竖直线形元素"""
    
    def test_create_grade_element(self):
        """测试创建坡段元素"""
        element = VerticalElement(
            element_type="grade",
            start_station=0.0,
            end_station=100.0,
            length=100.0,
            start_elevation=100.0,
            end_elevation=105.0,
            grade=5.0
        )
        assert element.element_type == "grade"
        assert element.grade == 5.0
    
    def test_create_vertical_curve(self):
        """测试创建竖曲线元素"""
        element = VerticalElement(
            element_type="vertical_curve",
            start_station=100.0,
            end_station=200.0,
            length=100.0,
            start_elevation=105.0,
            end_elevation=110.0,
            grade=5.0,
            radius=1000.0
        )
        assert element.element_type == "vertical_curve"
        assert element.radius == 1000.0


class TestRoadAlignment:
    """测试道路中线"""
    
    def test_create_alignment(self):
        """测试创建道路中线"""
        from src.models.road_design import StationingInfo
        
        stationing = StationingInfo(
            start_station=0.0,
            end_station=200.0,
            total_length=200.0
        )
        
        alignment = RoadAlignment(stationing=stationing)
        assert alignment.stationing.total_length == 200.0
        assert len(alignment.horizontal_alignment) == 0
        assert len(alignment.vertical_alignment) == 0
    
    def test_horizontal_continuity_validation(self):
        """测试水平线形连续性验证"""
        from src.models.road_design import StationingInfo
        
        # 创建不连续的水平线形
        h1 = HorizontalElement(
            element_type="straight",
            start_station=0.0,
            end_station=100.0,
            length=100.0,
            start_point=Point2D(x=0.0, y=0.0),
            end_point=Point2D(x=100.0, y=0.0)
        )
        
        h2 = HorizontalElement(
            element_type="straight",
            start_station=150.0,  # 不连续！
            end_station=250.0,
            length=100.0,
            start_point=Point2D(x=150.0, y=0.0),
            end_point=Point2D(x=250.0, y=0.0)
        )
        
        stationing = StationingInfo(
            start_station=0.0,
            end_station=250.0,
            total_length=250.0
        )
        
        with pytest.raises(ValueError):
            RoadAlignment(
                horizontal_alignment=[h1, h2],
                stationing=stationing
            )


class TestDesignStandards:
    """测试设计标准"""
    
    def test_create_design_standards(self):
        """测试创建设计标准"""
        standards = DesignStandards(
            standard_name="测试标准",
            version="v1.0",
            design_speed=60.0,
            min_horizontal_radius=100.0,
            max_grade=6.0,
            min_vertical_radius=600.0,
            road_width=7.0,
            shoulder_width=1.0
        )
        assert standards.standard_name == "测试标准"
        assert standards.design_speed == 60.0
    
    def test_default_design_standards(self):
        """测试默认设计标准"""
        standards = create_default_design_standards()
        assert standards.standard_name == "露天矿山道路设计标准"
        assert standards.design_speed == 40.0
        assert "min_sight_distance" in standards.parameters


class TestRoadDesign:
    """测试道路设计"""
    
    def test_create_road_design(self):
        """测试创建道路设计"""
        standards = create_default_design_standards()
        design = RoadDesign(
            name="测试道路",
            description="测试用道路设计",
            design_standards=standards
        )
        assert design.name == "测试道路"
        assert design.status == DesignStatus.DRAFT
        assert design.id is not None
    
    def test_sample_road_design(self):
        """测试示例道路设计"""
        design = create_sample_road_design()
        assert design.name == "示例道路设计"
        assert design.design_standards.standard_name == "露天矿山道路设计标准"
        assert design.designer == "系统"
    
    def test_road_design_with_alignment(self):
        """测试带中线的道路设计"""
        from src.models.road_design import StationingInfo
        
        standards = create_default_design_standards()
        stationing = StationingInfo(
            start_station=0.0,
            end_station=100.0,
            total_length=100.0
        )
        alignment = RoadAlignment(stationing=stationing)
        
        design = RoadDesign(
            name="带中线的道路",
            design_standards=standards,
            alignment=alignment
        )
        
        assert design.alignment is not None
        assert design.alignment.stationing.total_length == 100.0


class TestCrossSection:
    """测试横断面"""
    
    def test_create_cross_section(self):
        """测试创建横断面"""
        section = CrossSection(
            station=100.0,
            ground_profile=[Point2D(x=-10.0, y=100.0), Point2D(x=10.0, y=102.0)],
            design_profile=[Point2D(x=-5.0, y=101.0), Point2D(x=5.0, y=101.0)]
        )
        assert section.station == 100.0
        assert len(section.ground_profile) == 2
        assert section.road_width == 7.0  # 默认值


if __name__ == "__main__":
    pytest.main([__file__, "-v"])