"""
横断面设计模块
Cross Section Design Module
"""

from typing import List, Dict, Any, Optional, Tuple
import math
from dataclasses import dataclass

from src.core.geometry import Point2D, Point3D


@dataclass
class CrossSectionTemplate:
    """横断面模板"""
    name: str
    road_width: float = 7.0  # 路面宽度
    shoulder_width: float = 1.0  # 路肩宽度
    crown_slope: float = 2.0  # 路拱坡度 (%)
    shoulder_slope: float = 4.0  # 路肩坡度 (%)
    cut_slope_ratio: float = 1.5  # 挖方边坡坡率 (1:1.5)
    fill_slope_ratio: float = 1.5  # 填方边坡坡率 (1:1.5)
    ditch_width: float = 1.0  # 边沟宽度
    ditch_depth: float = 0.5  # 边沟深度


@dataclass
class GroundPoint:
    """地面点"""
    offset: float  # 距中线距离（左负右正）
    elevation: float  # 高程
    description: str = ""  # 描述


@dataclass
class DesignPoint:
    """设计点"""
    offset: float  # 距中线距离
    elevation: float  # 高程
    point_type: str  # 点类型：centerline, edge, shoulder, slope, ditch
    description: str = ""


@dataclass
class CrossSectionData:
    """横断面数据"""
    station: float  # 桩号
    centerline_elevation: float  # 中线高程
    ground_points: List[GroundPoint]  # 地面点
    design_points: List[DesignPoint]  # 设计点
    template: CrossSectionTemplate  # 使用的模板
    cut_area: float = 0.0  # 挖方面积
    fill_area: float = 0.0  # 填方面积


class CrossSectionDesigner:
    """横断面设计器"""
    
    def __init__(self, template: Optional[CrossSectionTemplate] = None):
        self.template = template or self._create_default_template()
    
    def _create_default_template(self) -> CrossSectionTemplate:
        """创建默认模板"""
        return CrossSectionTemplate(
            name="露天矿山道路标准断面",
            road_width=7.0,
            shoulder_width=1.0,
            crown_slope=2.0,
            shoulder_slope=4.0,
            cut_slope_ratio=1.5,
            fill_slope_ratio=1.5,
            ditch_width=1.0,
            ditch_depth=0.5
        )
    
    def design_cross_section(self, station: float, centerline_elevation: float,
                           ground_points: List[GroundPoint]) -> CrossSectionData:
        """设计横断面"""
        # 生成设计点
        design_points = self._generate_design_points(centerline_elevation)
        
        # 计算挖填面积
        cut_area, fill_area = self._calculate_cut_fill_areas(
            ground_points, design_points
        )
        
        return CrossSectionData(
            station=station,
            centerline_elevation=centerline_elevation,
            ground_points=ground_points,
            design_points=design_points,
            template=self.template,
            cut_area=cut_area,
            fill_area=fill_area
        )
    
    def _generate_design_points(self, centerline_elevation: float) -> List[DesignPoint]:
        """生成设计点"""
        points = []
        
        # 中线点
        points.append(DesignPoint(
            offset=0.0,
            elevation=centerline_elevation,
            point_type="centerline",
            description="道路中线"
        ))
        
        # 路面边缘点（考虑路拱）
        crown_drop = (self.template.road_width / 2) * (self.template.crown_slope / 100)
        
        # 左侧路面边缘
        points.append(DesignPoint(
            offset=-self.template.road_width / 2,
            elevation=centerline_elevation - crown_drop,
            point_type="edge",
            description="左侧路面边缘"
        ))
        
        # 右侧路面边缘
        points.append(DesignPoint(
            offset=self.template.road_width / 2,
            elevation=centerline_elevation - crown_drop,
            point_type="edge",
            description="右侧路面边缘"
        ))
        
        # 路肩边缘点
        shoulder_drop = self.template.shoulder_width * (self.template.shoulder_slope / 100)
        
        # 左侧路肩边缘
        left_shoulder_elevation = centerline_elevation - crown_drop - shoulder_drop
        points.append(DesignPoint(
            offset=-(self.template.road_width / 2 + self.template.shoulder_width),
            elevation=left_shoulder_elevation,
            point_type="shoulder",
            description="左侧路肩边缘"
        ))
        
        # 右侧路肩边缘
        right_shoulder_elevation = centerline_elevation - crown_drop - shoulder_drop
        points.append(DesignPoint(
            offset=self.template.road_width / 2 + self.template.shoulder_width,
            elevation=right_shoulder_elevation,
            point_type="shoulder",
            description="右侧路肩边缘"
        ))
        
        return sorted(points, key=lambda p: p.offset)
    
    def _calculate_cut_fill_areas(self, ground_points: List[GroundPoint],
                                design_points: List[DesignPoint]) -> Tuple[float, float]:
        """计算挖填面积"""
        if not ground_points or not design_points:
            return 0.0, 0.0
        
        # 简化的面积计算方法
        cut_area = 0.0
        fill_area = 0.0
        
        # 对每个设计点，找到对应的地面高程
        for design_point in design_points:
            ground_elevation = self._interpolate_ground_elevation(
                ground_points, design_point.offset
            )
            
            if ground_elevation is None:
                continue
            
            # 计算高差
            height_diff = design_point.elevation - ground_elevation
            
            # 估算影响宽度（简化处理）
            width = 1.0  # 1米宽度
            
            if height_diff > 0:  # 填方
                fill_area += height_diff * width
            else:  # 挖方
                cut_area += abs(height_diff) * width
        
        return cut_area, fill_area
    
    def _interpolate_ground_elevation(self, ground_points: List[GroundPoint],
                                    offset: float) -> Optional[float]:
        """插值计算地面高程"""
        if not ground_points:
            return None
        
        # 按偏距排序
        sorted_points = sorted(ground_points, key=lambda p: p.offset)
        
        # 如果偏距在范围外，返回最近点的高程
        if offset <= sorted_points[0].offset:
            return sorted_points[0].elevation
        if offset >= sorted_points[-1].offset:
            return sorted_points[-1].elevation
        
        # 线性插值
        for i in range(len(sorted_points) - 1):
            p1 = sorted_points[i]
            p2 = sorted_points[i + 1]
            
            if p1.offset <= offset <= p2.offset:
                if p2.offset == p1.offset:
                    return p1.elevation
                
                ratio = (offset - p1.offset) / (p2.offset - p1.offset)
                return p1.elevation + ratio * (p2.elevation - p1.elevation)
        
        return None
    
    def generate_slope_points(self, cross_section: CrossSectionData,
                            max_offset: float = 50.0) -> List[DesignPoint]:
        """生成边坡点"""
        slope_points = []
        
        # 获取路肩边缘点
        left_shoulder = None
        right_shoulder = None
        
        for point in cross_section.design_points:
            if point.point_type == "shoulder":
                if point.offset < 0:
                    left_shoulder = point
                else:
                    right_shoulder = point
        
        if not left_shoulder or not right_shoulder:
            return slope_points
        
        # 生成左侧边坡
        left_slope_points = self._generate_side_slope_points(
            left_shoulder, cross_section.ground_points, -max_offset, True
        )
        slope_points.extend(left_slope_points)
        
        # 生成右侧边坡
        right_slope_points = self._generate_side_slope_points(
            right_shoulder, cross_section.ground_points, max_offset, False
        )
        slope_points.extend(right_slope_points)
        
        return slope_points
    
    def _generate_side_slope_points(self, shoulder_point: DesignPoint,
                                  ground_points: List[GroundPoint],
                                  max_offset: float, is_left: bool) -> List[DesignPoint]:
        """生成单侧边坡点"""
        slope_points = []
        
        current_offset = shoulder_point.offset
        current_elevation = shoulder_point.elevation
        
        # 确定边坡方向和坡率
        direction = -1 if is_left else 1
        
        step = 1.0 * direction  # 1米步长
        
        while abs(current_offset) < abs(max_offset):
            current_offset += step
            
            # 获取地面高程
            ground_elevation = self._interpolate_ground_elevation(
                ground_points, current_offset
            )
            
            if ground_elevation is None:
                break
            
            # 判断挖方还是填方
            if current_elevation > ground_elevation:
                # 挖方边坡
                slope_ratio = self.template.cut_slope_ratio
                slope_elevation = current_elevation - abs(step) / slope_ratio
            else:
                # 填方边坡
                slope_ratio = self.template.fill_slope_ratio
                slope_elevation = current_elevation - abs(step) / slope_ratio
            
            # 检查是否与地面相交
            if (current_elevation > ground_elevation and slope_elevation <= ground_elevation) or \
               (current_elevation < ground_elevation and slope_elevation >= ground_elevation):
                # 边坡与地面相交
                slope_points.append(DesignPoint(
                    offset=current_offset,
                    elevation=ground_elevation,
                    point_type="slope",
                    description="边坡与地面交点"
                ))
                break
            
            slope_points.append(DesignPoint(
                offset=current_offset,
                elevation=slope_elevation,
                point_type="slope",
                description="边坡点"
            ))
            
            current_elevation = slope_elevation
        
        return slope_points
    
    def add_drainage_ditch(self, cross_section: CrossSectionData) -> List[DesignPoint]:
        """添加排水沟"""
        ditch_points = []
        
        # 在挖方段添加边沟
        if cross_section.cut_area > 0:
            # 找到边坡脚点
            slope_points = [p for p in cross_section.design_points if p.point_type == "slope"]
            
            for slope_point in slope_points:
                if slope_point.offset < 0:  # 左侧
                    ditch_offset = slope_point.offset - self.template.ditch_width
                else:  # 右侧
                    ditch_offset = slope_point.offset + self.template.ditch_width
                
                # 边沟底点
                ditch_bottom = DesignPoint(
                    offset=ditch_offset,
                    elevation=slope_point.elevation - self.template.ditch_depth,
                    point_type="ditch",
                    description="边沟底"
                )
                ditch_points.append(ditch_bottom)
        
        return ditch_points


class CrossSectionAnalyzer:
    """横断面分析器"""
    
    @staticmethod
    def calculate_earthwork_volume(cross_sections: List[CrossSectionData]) -> Dict[str, float]:
        """计算土方量"""
        if len(cross_sections) < 2:
            return {"cut_volume": 0.0, "fill_volume": 0.0, "net_volume": 0.0}
        
        total_cut_volume = 0.0
        total_fill_volume = 0.0
        
        # 使用平均断面法计算体积
        for i in range(len(cross_sections) - 1):
            section1 = cross_sections[i]
            section2 = cross_sections[i + 1]
            
            # 计算桩号间距
            distance = abs(section2.station - section1.station)
            
            # 平均面积
            avg_cut_area = (section1.cut_area + section2.cut_area) / 2
            avg_fill_area = (section1.fill_area + section2.fill_area) / 2
            
            # 计算体积
            cut_volume = avg_cut_area * distance
            fill_volume = avg_fill_area * distance
            
            total_cut_volume += cut_volume
            total_fill_volume += fill_volume
        
        net_volume = total_fill_volume - total_cut_volume
        
        return {
            "cut_volume": total_cut_volume,
            "fill_volume": total_fill_volume,
            "net_volume": net_volume
        }
    
    @staticmethod
    def analyze_cross_section_stability(cross_section: CrossSectionData) -> Dict[str, Any]:
        """分析横断面稳定性"""
        analysis = {
            "stable": True,
            "warnings": [],
            "recommendations": []
        }
        
        # 检查边坡坡率
        template = cross_section.template
        
        if template.cut_slope_ratio < 1.0:
            analysis["stable"] = False
            analysis["warnings"].append("挖方边坡过陡")
            analysis["recommendations"].append("增大挖方边坡坡率至1:1.5以上")
        
        if template.fill_slope_ratio < 1.2:
            analysis["stable"] = False
            analysis["warnings"].append("填方边坡过陡")
            analysis["recommendations"].append("增大填方边坡坡率至1:1.5以上")
        
        # 检查挖填平衡
        if cross_section.cut_area > 0 and cross_section.fill_area > 0:
            imbalance_ratio = abs(cross_section.cut_area - cross_section.fill_area) / max(cross_section.cut_area, cross_section.fill_area)
            if imbalance_ratio > 0.5:
                analysis["warnings"].append("挖填不平衡")
                analysis["recommendations"].append("优化断面设计以改善挖填平衡")
        
        return analysis


# 工厂函数
def create_mining_road_template() -> CrossSectionTemplate:
    """创建露天矿山道路模板"""
    return CrossSectionTemplate(
        name="露天矿山道路标准断面",
        road_width=7.0,
        shoulder_width=1.0,
        crown_slope=2.0,
        shoulder_slope=4.0,
        cut_slope_ratio=1.5,
        fill_slope_ratio=1.5,
        ditch_width=1.0,
        ditch_depth=0.5
    )


def create_cross_section_designer(template_name: str = "standard") -> CrossSectionDesigner:
    """创建横断面设计器"""
    if template_name == "mining":
        template = create_mining_road_template()
    else:
        template = None  # 使用默认模板
    
    return CrossSectionDesigner(template)


def create_sample_ground_points() -> List[GroundPoint]:
    """创建示例地面点"""
    return [
        GroundPoint(-20.0, 102.0, "左侧地面"),
        GroundPoint(-10.0, 101.5, "左侧地面"),
        GroundPoint(-5.0, 101.0, "左侧地面"),
        GroundPoint(0.0, 100.5, "中线地面"),
        GroundPoint(5.0, 100.8, "右侧地面"),
        GroundPoint(10.0, 101.2, "右侧地面"),
        GroundPoint(20.0, 101.8, "右侧地面")
    ]


def create_sample_cross_section() -> CrossSectionData:
    """创建示例横断面"""
    designer = create_cross_section_designer("mining")
    ground_points = create_sample_ground_points()
    
    return designer.design_cross_section(
        station=100.0,
        centerline_elevation=101.0,
        ground_points=ground_points
    )