"""
数据库连接和管理
Database Connection and Management
"""

try:
    from sqlalchemy import create_engine, MetaData
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy.pool import StaticPool
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    print("⚠️  SQLAlchemy未安装，数据库功能暂时不可用")

import asyncio
from src.core.config import settings

if SQLALCHEMY_AVAILABLE:
    # 创建数据库引擎
    try:
        # 根据数据库类型配置连接参数
        connect_args = {}
        if "sqlite" in settings.DATABASE_URL:
            connect_args = {"check_same_thread": False}
        elif "postgresql" in settings.DATABASE_URL:
            connect_args = {"options": "-c timezone=UTC"}

        engine = create_engine(
            settings.DATABASE_URL,
            echo=settings.DATABASE_ECHO,
            poolclass=StaticPool if "sqlite" in settings.DATABASE_URL else None,
            connect_args=connect_args
        )
        
        # 创建会话工厂
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # 创建基础模型类
        Base = declarative_base()
        
        # 元数据
        metadata = MetaData()
        
    except Exception as e:
        print(f"⚠️  数据库引擎创建失败: {e}")
        engine = None
        SessionLocal = None
        Base = None
        metadata = None
else:
    engine = None
    SessionLocal = None
    Base = None
    metadata = None


def get_db():
    """获取数据库会话"""
    if not SQLALCHEMY_AVAILABLE or SessionLocal is None:
        raise RuntimeError("数据库不可用")
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def init_db():
    """初始化数据库"""
    if not SQLALCHEMY_AVAILABLE or engine is None:
        print("⚠️  数据库不可用，跳过初始化")
        return

    try:
        # 检查数据库连接
        with engine.connect() as conn:
            # 根据数据库类型进行不同的初始化
            if "postgresql" in settings.DATABASE_URL:
                try:
                    conn.execute("CREATE EXTENSION IF NOT EXISTS postgis;")
                    conn.execute("CREATE EXTENSION IF NOT EXISTS postgis_topology;")
                    conn.commit()
                    print("✅ PostGIS扩展已启用")
                except Exception as e:
                    print(f"⚠️  PostGIS扩展启用失败: {e}")
            elif "sqlite" in settings.DATABASE_URL:
                # SQLite初始化
                print("✅ SQLite数据库已连接")
                # 创建数据库表（如果需要）
                try:
                    from src.models.base import Base
                    if Base is not None:
                        Base.metadata.create_all(bind=engine)
                        print("✅ 数据库表结构已创建")
                except Exception as e:
                    print(f"⚠️  数据库表创建失败: {e}")

            print("✅ 数据库连接成功")

    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        # 不抛出异常，允许应用继续运行


def create_tables():
    """创建数据库表"""
    if not SQLALCHEMY_AVAILABLE or Base is None or engine is None:
        print("⚠️  数据库不可用，跳过表创建")
        return
        
    try:
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建成功")
    except Exception as e:
        print(f"❌ 数据库表创建失败: {e}")
        raise


def drop_tables():
    """删除所有表（开发环境使用）"""
    if not SQLALCHEMY_AVAILABLE or Base is None or engine is None:
        print("⚠️  数据库不可用，跳过表删除")
        return
        
    if settings.DEBUG:
        Base.metadata.drop_all(bind=engine)
        print("⚠️  所有数据库表已删除")
    else:
        print("❌ 生产环境不允许删除表")


class DatabaseManager:
    """数据库管理器"""
    
    @staticmethod
    def check_connection() -> bool:
        """检查数据库连接状态"""
        if not SQLALCHEMY_AVAILABLE or engine is None:
            return False
            
        try:
            with engine.connect() as conn:
                conn.execute("SELECT 1")
                return True
        except Exception:
            return False
    
    @staticmethod
    def get_table_info():
        """获取数据库表信息"""
        if not SQLALCHEMY_AVAILABLE or engine is None:
            return []
            
        try:
            with engine.connect() as conn:
                result = conn.execute("""
                    SELECT table_name, table_type 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """)
                return result.fetchall()
        except Exception as e:
            print(f"获取表信息失败: {e}")
            return []
    
    @staticmethod
    def backup_database(backup_path: str):
        """备份数据库（PostgreSQL）"""
        if not SQLALCHEMY_AVAILABLE:
            print("数据库不可用，无法备份")
            return
            
        if "postgresql" in settings.DATABASE_URL:
            import subprocess
            import os
            
            # 解析数据库URL
            db_url = settings.DATABASE_URL
            # 这里需要实现具体的备份逻辑
            print(f"数据库备份功能待实现: {backup_path}")
        else:
            print("当前数据库类型不支持自动备份")


# 全局数据库管理器实例
db_manager = DatabaseManager()