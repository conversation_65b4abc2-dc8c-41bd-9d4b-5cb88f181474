"""
智能启动脚本 - 根据依赖可用性选择启动方式
Smart Startup Script - Choose startup method based on dependency availability
"""

import os
import sys
import subprocess
import importlib
import time

def check_dependency(module_name):
    """检查依赖是否可用"""
    try:
        importlib.import_module(module_name)
        return True
    except ImportError:
        return False

def install_dependencies():
    """尝试安装依赖"""
    print("🔧 尝试安装基础依赖...")
    
    basic_deps = [
        "fastapi",
        "uvicorn",
        "sqlalchemy", 
        "pydantic",
        "python-dotenv"
    ]
    
    for dep in basic_deps:
        try:
            print(f"   安装 {dep}...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "--user", dep
            ], check=True, capture_output=True)
            print(f"   ✅ {dep} 安装成功")
        except subprocess.CalledProcessError:
            print(f"   ❌ {dep} 安装失败")
            return False
    
    return True

def start_fastapi_app():
    """启动FastAPI应用"""
    print("🚀 启动FastAPI应用...")
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = os.getcwd()
    
    try:
        # 尝试导入并启动应用
        sys.path.insert(0, os.getcwd())
        from src.main import app
        import uvicorn
        
        print("✅ FastAPI应用模块导入成功")
        print("   访问地址: http://localhost:8000")
        print("   API文档: http://localhost:8000/docs")
        print("=" * 60)
        
        uvicorn.run(
            "src.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True
        )
        
    except Exception as e:
        print(f"❌ FastAPI应用启动失败: {e}")
        return False
    
    return True

def start_simple_app():
    """启动简化应用"""
    print("🚀 启动简化版应用...")
    
    try:
        # 导入并启动简化应用
        sys.path.insert(0, os.getcwd())
        import simple_app
        simple_app.start_server()
        
    except Exception as e:
        print(f"❌ 简化应用启动失败: {e}")
        return False
    
    return True

def create_env_file():
    """创建环境配置文件"""
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            print("🔧 创建环境配置文件...")
            with open(".env.example", "r", encoding="utf-8") as src:
                with open(".env", "w", encoding="utf-8") as dst:
                    dst.write(src.read())
            print("✅ .env 文件已创建")
        else:
            # 创建基础的.env文件
            env_content = """# 应用配置
APP_NAME=露天矿山道路设计软件
VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=sqlite:///./mining_roads.db
DATABASE_ECHO=true

# 日志配置
LOG_LEVEL=DEBUG
LOG_FILE=./logs/app.log
"""
            with open(".env", "w", encoding="utf-8") as f:
                f.write(env_content)
            print("✅ 基础 .env 文件已创建")

def create_directories():
    """创建必要的目录"""
    dirs = ["uploads", "logs", "static", "config/design_standards"]
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)

def main():
    """主函数"""
    print("=" * 60)
    print("露天矿山道路设计软件 - 智能启动脚本")
    print("Mining Road Design Software - Smart Startup Script")
    print("=" * 60)
    
    # 检查Python版本
    print(f"🔍 Python版本: {sys.version}")
    
    # 创建必要的文件和目录
    create_env_file()
    create_directories()
    
    # 检查依赖
    print("\n🔍 检查依赖可用性...")
    fastapi_available = check_dependency("fastapi")
    uvicorn_available = check_dependency("uvicorn")
    sqlalchemy_available = check_dependency("sqlalchemy")
    
    print(f"   FastAPI: {'✅' if fastapi_available else '❌'}")
    print(f"   Uvicorn: {'✅' if uvicorn_available else '❌'}")
    print(f"   SQLAlchemy: {'✅' if sqlalchemy_available else '❌'}")
    
    # 决定启动方式
    if fastapi_available and uvicorn_available:
        print("\n✅ 完整依赖可用，启动FastAPI应用")
        start_fastapi_app()
    else:
        print("\n⚠️  部分依赖不可用")
        
        # 询问是否安装依赖
        try:
            choice = input("是否尝试安装依赖? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                if install_dependencies():
                    print("✅ 依赖安装成功，重新启动...")
                    time.sleep(2)
                    # 重新检查并启动
                    if check_dependency("fastapi") and check_dependency("uvicorn"):
                        start_fastapi_app()
                    else:
                        print("❌ 依赖安装后仍不可用，启动简化版应用")
                        start_simple_app()
                else:
                    print("❌ 依赖安装失败，启动简化版应用")
                    start_simple_app()
            else:
                print("启动简化版应用...")
                start_simple_app()
        except KeyboardInterrupt:
            print("\n🛑 用户取消操作")
            sys.exit(0)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 应用已停止")
        sys.exit(0)
