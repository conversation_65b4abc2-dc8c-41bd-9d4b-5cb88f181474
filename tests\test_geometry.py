"""
几何计算模块测试
Geometry Calculation Module Tests
"""

import math
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.geometry import (
    Point2D, Point3D, GeometryCalculator,
    StraightLineCalculator, CircularCurveCalculator,
    HorizontalAlignmentCalculator, VerticalAlignmentCalculator
)


def test_point2d():
    """测试二维点"""
    print("🔍 测试二维点...")
    
    # 创建点
    p1 = Point2D(0, 0)
    p2 = Point2D(3, 4)
    
    # 测试距离计算
    distance = p1.distance_to(p2)
    expected_distance = 5.0  # 3-4-5直角三角形
    assert abs(distance - expected_distance) < 0.001, f"距离计算错误: {distance}"
    print(f"✅ 距离计算正确: {distance}")
    
    # 测试角度计算
    angle = p1.angle_to(p2)
    expected_angle = math.atan2(4, 3)
    assert abs(angle - expected_angle) < 0.001, f"角度计算错误: {angle}"
    print(f"✅ 角度计算正确: {math.degrees(angle):.1f}°")
    
    # 测试平移
    p3 = p1.translate(10, 20)
    assert p3.x == 10 and p3.y == 20, f"平移错误: ({p3.x}, {p3.y})"
    print(f"✅ 平移正确: ({p3.x}, {p3.y})")


def test_geometry_calculator():
    """测试几何计算器"""
    print("\n🔍 测试几何计算器...")
    
    calc = GeometryCalculator()
    
    # 测试角度标准化
    angle = calc.normalize_angle(-math.pi / 2)
    expected = 3 * math.pi / 2
    assert abs(angle - expected) < 0.001, f"角度标准化错误: {angle}"
    print(f"✅ 角度标准化正确: {math.degrees(angle):.1f}°")
    
    # 测试方位角计算
    p1 = Point2D(0, 0)
    p2 = Point2D(1, 1)
    bearing = calc.calculate_bearing(p1, p2)
    print(f"✅ 方位角计算: {math.degrees(bearing):.1f}°")
    
    # 测试直线上的点
    p3 = calc.point_on_line(p1, p2, math.sqrt(2) / 2)
    expected_x = expected_y = 0.5
    assert abs(p3.x - expected_x) < 0.001 and abs(p3.y - expected_y) < 0.001
    print(f"✅ 直线插值正确: ({p3.x:.3f}, {p3.y:.3f})")


def test_straight_line_calculator():
    """测试直线计算器"""
    print("\n🔍 测试直线计算器...")
    
    calc = StraightLineCalculator()
    
    # 测试点计算
    start = Point2D(0, 0)
    end = Point2D(100, 0)
    points = calc.calculate_points(start, end, 20.0)
    
    expected_count = 6  # 0, 20, 40, 60, 80, 100
    assert len(points) == expected_count, f"点数量错误: {len(points)}"
    print(f"✅ 直线点计算正确: {len(points)} 个点")
    
    # 验证点位置
    assert points[0].x == 0 and points[0].y == 0, "起点错误"
    assert points[-1].x == 100 and points[-1].y == 0, "终点错误"
    assert points[1].x == 20 and points[1].y == 0, "中间点错误"
    print("✅ 点位置正确")
    
    # 测试长度计算
    length = calc.calculate_length(start, end)
    assert length == 100.0, f"长度计算错误: {length}"
    print(f"✅ 长度计算正确: {length}m")


def test_circular_curve_calculator():
    """测试圆曲线计算器"""
    print("\n🔍 测试圆曲线计算器...")
    
    calc = CircularCurveCalculator()
    
    # 测试圆心计算
    start = Point2D(0, 0)
    end = Point2D(10, 0)
    radius = 10.0
    
    try:
        center = calc.calculate_center(start, end, radius, clockwise=True)
        print(f"✅ 圆心计算: ({center.x:.3f}, {center.y:.3f})")
        
        # 验证圆心到起点和终点的距离
        dist1 = center.distance_to(start)
        dist2 = center.distance_to(end)
        assert abs(dist1 - radius) < 0.001, f"圆心到起点距离错误: {dist1}"
        assert abs(dist2 - radius) < 0.001, f"圆心到终点距离错误: {dist2}"
        print("✅ 圆心位置验证正确")
        
    except ValueError as e:
        print(f"⚠️  圆心计算异常: {e}")
    
    # 测试弧长计算
    arc_length = calc.calculate_arc_length(radius, math.pi / 2)  # 90度
    expected_length = radius * math.pi / 2
    assert abs(arc_length - expected_length) < 0.001, f"弧长计算错误: {arc_length}"
    print(f"✅ 弧长计算正确: {arc_length:.3f}m")


def test_vertical_alignment_calculator():
    """测试竖直线形计算器"""
    print("\n🔍 测试竖直线形计算器...")
    
    calc = VerticalAlignmentCalculator()
    
    # 测试坡段高程计算
    elevation = calc.calculate_grade_elevation(0, 100, 100, 105, 50)
    expected_elevation = 102.5  # 中点高程
    assert abs(elevation - expected_elevation) < 0.001, f"坡段高程错误: {elevation}"
    print(f"✅ 坡段高程计算正确: {elevation}m")
    
    # 测试坡度计算
    grade = calc.calculate_grade_percentage(100, 105, 100)
    expected_grade = 5.0  # 5%
    assert abs(grade - expected_grade) < 0.001, f"坡度计算错误: {grade}"
    print(f"✅ 坡度计算正确: {grade}%")
    
    # 测试坡度验证
    is_valid = calc.validate_grade_limits(6.0, 8.0)
    assert is_valid, "坡度验证错误"
    print("✅ 坡度验证正确")
    
    is_invalid = calc.validate_grade_limits(10.0, 8.0)
    assert not is_invalid, "坡度验证错误"
    print("✅ 超限坡度验证正确")


def test_horizontal_alignment_calculator():
    """测试水平线形计算器"""
    print("\n🔍 测试水平线形计算器...")
    
    calc = HorizontalAlignmentCalculator()
    
    # 创建简单的线形元素
    elements = [
        {
            'type': 'straight',
            'start_x': 0, 'start_y': 0,
            'end_x': 100, 'end_y': 0,
            'length': 100
        },
        {
            'type': 'straight',
            'start_x': 100, 'start_y': 0,
            'end_x': 200, 'end_y': 100,
            'length': math.sqrt(10000 + 10000)
        }
    ]
    
    # 测试点计算
    points = calc.calculate_alignment_points(elements, 50.0)
    assert len(points) > 0, "未生成线形点"
    print(f"✅ 线形点计算: {len(points)} 个点")
    
    # 测试连续性验证
    errors = calc.validate_alignment_continuity(elements)
    assert len(errors) == 0, f"连续性验证失败: {errors}"
    print("✅ 线形连续性验证通过")


def run_all_tests():
    """运行所有测试"""
    print("=" * 70)
    print("道路几何计算模块测试")
    print("Road Geometry Calculation Module Tests")
    print("=" * 70)
    
    try:
        test_point2d()
        test_geometry_calculator()
        test_straight_line_calculator()
        test_circular_curve_calculator()
        test_vertical_alignment_calculator()
        test_horizontal_alignment_calculator()
        
        print("\n" + "=" * 70)
        print("🎉 所有几何计算测试通过！")
        print("✅ 几何计算模块功能正常")
        print("=" * 70)
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        print("=" * 70)
        return False


if __name__ == "__main__":
    run_all_tests()