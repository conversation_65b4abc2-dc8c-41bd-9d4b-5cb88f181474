"""
API v1 路由汇总
API v1 Router Collection
"""

from fastapi import APIRouter

# 导入各个模块的路由（暂时创建占位符）
# from src.api.v1.endpoints import roads, gis, cad, optimization, detection

# 创建API路由器
api_router = APIRouter()

# 系统信息路由
@api_router.get("/info")
async def api_info():
    """API信息"""
    return {
        "api_version": "v1",
        "description": "露天矿山道路设计软件API",
        "endpoints": {
            "roads": "/roads - 道路设计相关接口",
            "gis": "/gis - GIS数据处理接口", 
            "cad": "/cad - CAD文件处理接口",
            "optimization": "/optimization - 路线优化接口",
            "detection": "/detection - 冲突检测接口"
        }
    }

# 健康检查路由
@api_router.get("/health")
async def api_health():
    """API健康检查"""
    from src.core.database import db_manager
    
    db_status = db_manager.check_connection()
    
    return {
        "status": "healthy" if db_status else "unhealthy",
        "database": "connected" if db_status else "disconnected",
        "services": {
            "database": db_status,
            "redis": True,  # 待实现Redis检查
            "celery": True  # 待实现Celery检查
        }
    }

# 注册子路由（后续任务中实现）
# api_router.include_router(roads.router, prefix="/roads", tags=["roads"])
# api_router.include_router(gis.router, prefix="/gis", tags=["gis"])
# api_router.include_router(cad.router, prefix="/cad", tags=["cad"])
# api_router.include_router(optimization.router, prefix="/optimization", tags=["optimization"])
# api_router.include_router(detection.router, prefix="/detection", tags=["detection"])