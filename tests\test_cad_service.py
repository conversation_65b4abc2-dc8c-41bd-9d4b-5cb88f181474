"""
CAD数据处理服务测试
CAD Data Processing Service Tests
"""

import sys
import os
import tempfile

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.cad_service import (
    CADImporter, CADExporter, CADConverter, CADAnalyzer,
    create_cad_importer, create_cad_exporter, create_cad_converter,
    import_cad_file, export_road_design_to_cad, convert_terrain_to_cad,
    get_supported_cad_formats, create_sample_dxf_file,
    CADEntityType, CADEntity, CADLayer, CADDrawing
)
from src.services.gis_service import TerrainPoint


def test_cad_importer():
    """测试CAD导入器"""
    print("🔍 测试CAD导入器...")
    
    importer = create_cad_importer()
    
    # 测试支持的格式
    formats = importer.get_supported_formats()
    print(f"✅ 支持的CAD格式: {', '.join(formats)}")
    
    # 测试文件不存在
    result = importer.import_cad_file("nonexistent.dxf")
    assert not result.success, "不存在的文件应该导入失败"
    print("✅ 不存在文件处理正确")
    
    # 测试不支持的格式
    with tempfile.NamedTemporaryFile(suffix='.unknown', delete=False) as f:
        temp_file = f.name
    
    try:
        result = importer.import_cad_file(temp_file)
        assert not result.success, "不支持的格式应该导入失败"
        print("✅ 不支持格式处理正确")
    finally:
        os.unlink(temp_file)


def test_sample_dxf_creation():
    """测试示例DXF文件创建"""
    print("\n🔍 测试示例DXF文件创建...")
    
    with tempfile.NamedTemporaryFile(suffix='.dxf', delete=False) as f:
        dxf_file = f.name
    
    try:
        create_sample_dxf_file(dxf_file)
        
        # 验证文件是否创建
        assert os.path.exists(dxf_file), "DXF文件未创建"
        assert os.path.getsize(dxf_file) > 0, "DXF文件为空"
        
        print(f"✅ 示例DXF文件创建成功: {os.path.getsize(dxf_file)} 字节")
        
        # 尝试导入创建的文件
        result = import_cad_file(dxf_file)
        if result.success:
            print(f"✅ 示例DXF文件导入成功: {result.entity_count} 个实体")
            print(f"   图层数量: {result.layer_count}")
        else:
            print(f"⚠️  示例DXF文件导入失败: {result.message}")
        
    finally:
        if os.path.exists(dxf_file):
            os.unlink(dxf_file)


def test_cad_entities():
    """测试CAD实体"""
    print("\n🔍 测试CAD实体...")
    
    # 创建线实体
    line_entity = CADEntity(
        entity_type=CADEntityType.LINE,
        layer="0",
        color=1,
        geometry={
            'start': [0.0, 0.0, 0.0],
            'end': [100.0, 100.0, 0.0]
        }
    )
    
    assert line_entity.entity_type == CADEntityType.LINE
    assert line_entity.layer == "0"
    assert line_entity.geometry['start'] == [0.0, 0.0, 0.0]
    print("✅ 线实体创建正确")
    
    # 创建圆实体
    circle_entity = CADEntity(
        entity_type=CADEntityType.CIRCLE,
        layer="GEOMETRY",
        color=2,
        geometry={
            'center': [50.0, 50.0, 0.0],
            'radius': 25.0
        }
    )
    
    assert circle_entity.entity_type == CADEntityType.CIRCLE
    assert circle_entity.geometry['radius'] == 25.0
    print("✅ 圆实体创建正确")
    
    # 创建多段线实体
    polyline_entity = CADEntity(
        entity_type=CADEntityType.POLYLINE,
        layer="BOUNDARY",
        geometry={
            'points': [[0, 0, 0], [50, 0, 0], [50, 50, 0], [0, 50, 0]],
            'closed': True
        }
    )
    
    assert len(polyline_entity.geometry['points']) == 4
    assert polyline_entity.geometry['closed'] == True
    print("✅ 多段线实体创建正确")


def test_cad_drawing():
    """测试CAD图纸"""
    print("\n🔍 测试CAD图纸...")
    
    # 创建图层
    layer1 = CADLayer(name="CENTERLINE", color=1, entities=[])
    layer2 = CADLayer(name="BOUNDARY", color=2, entities=[])
    
    # 创建实体
    line_entity = CADEntity(
        entity_type=CADEntityType.LINE,
        layer="CENTERLINE",
        geometry={'start': [0, 0, 0], 'end': [100, 0, 0]}
    )
    
    circle_entity = CADEntity(
        entity_type=CADEntityType.CIRCLE,
        layer="BOUNDARY",
        geometry={'center': [50, 50, 0], 'radius': 25}
    )
    
    layer1.entities.append(line_entity)
    layer2.entities.append(circle_entity)
    
    # 创建图纸
    from src.services.gis_service import BoundingBox3D
    bounds = BoundingBox3D(0, 0, 0, 100, 100, 0)
    
    drawing = CADDrawing(
        filename="test.dxf",
        layers=[layer1, layer2],
        bounds=bounds,
        entity_count=2,
        units="Meters"
    )
    
    assert drawing.filename == "test.dxf"
    assert len(drawing.layers) == 2
    assert drawing.entity_count == 2
    print("✅ CAD图纸创建正确")


def test_cad_analyzer():
    """测试CAD分析器"""
    print("\n🔍 测试CAD分析器...")
    
    # 创建测试图纸
    layer = CADLayer(name="TEST", color=1)
    
    # 添加不同类型的实体
    entities = [
        CADEntity(CADEntityType.LINE, "TEST", geometry={'start': [0, 0, 0], 'end': [10, 10, 0]}),
        CADEntity(CADEntityType.CIRCLE, "TEST", geometry={'center': [5, 5, 0], 'radius': 3}),
        CADEntity(CADEntityType.POINT, "TEST", geometry={'location': [15, 15, 0]})
    ]
    
    layer.entities = entities
    
    from src.services.gis_service import BoundingBox3D
    bounds = BoundingBox3D(0, 0, 0, 15, 15, 0)
    
    drawing = CADDrawing(
        filename="test.dxf",
        layers=[layer],
        bounds=bounds,
        entity_count=3
    )
    
    # 分析图纸
    analysis = CADAnalyzer.analyze_drawing(drawing)
    
    assert analysis['basic_info']['entity_count'] == 3
    assert analysis['basic_info']['layer_count'] == 1
    assert CADEntityType.LINE in analysis['entity_types']
    assert CADEntityType.CIRCLE in analysis['entity_types']
    assert CADEntityType.POINT in analysis['entity_types']
    
    print("✅ CAD图纸分析正确")
    print(f"   实体类型统计: {analysis['entity_types']}")
    print(f"   图纸尺寸: {analysis['bounds']['width']} x {analysis['bounds']['height']}")
    
    # 测试点提取
    points = CADAnalyzer.extract_points(drawing)
    assert len(points) >= 3, "应该提取到至少3个点"
    print(f"✅ 点提取正确: {len(points)} 个点")
    
    # 测试线条提取
    lines = CADAnalyzer.extract_lines(drawing)
    assert len(lines) >= 1, "应该提取到至少1条线"
    print(f"✅ 线条提取正确: {len(lines)} 条线")


def test_cad_exporter():
    """测试CAD导出器"""
    print("\n🔍 测试CAD导出器...")
    
    exporter = create_cad_exporter()
    
    # 测试支持的格式
    formats = exporter.get_supported_formats()
    assert '.dxf' in formats, "应该支持DXF格式"
    print(f"✅ 支持的导出格式: {', '.join(formats)}")
    
    # 创建测试图纸
    layer = CADLayer(name="EXPORT_TEST", color=3)
    entities = [
        CADEntity(CADEntityType.LINE, "EXPORT_TEST", 
                 geometry={'start': [0, 0, 0], 'end': [50, 50, 0]}),
        CADEntity(CADEntityType.CIRCLE, "EXPORT_TEST", 
                 geometry={'center': [25, 25, 0], 'radius': 10})
    ]
    layer.entities = entities
    
    from src.services.gis_service import BoundingBox3D
    bounds = BoundingBox3D(0, 0, 0, 50, 50, 0)
    
    drawing = CADDrawing(
        filename="export_test.dxf",
        layers=[layer],
        bounds=bounds,
        entity_count=2
    )
    
    # 导出测试
    with tempfile.NamedTemporaryFile(suffix='.dxf', delete=False) as f:
        output_file = f.name
    
    try:
        result = exporter.export_drawing(drawing, output_file)
        
        if result.success:
            print(f"✅ CAD导出成功: {result.entity_count} 个实体")
            print(f"   文件大小: {result.file_size} 字节")
            
            # 验证文件存在
            assert os.path.exists(output_file), "导出文件不存在"
            assert os.path.getsize(output_file) > 0, "导出文件为空"
        else:
            print(f"⚠️  CAD导出失败: {result.message}")
        
    finally:
        if os.path.exists(output_file):
            os.unlink(output_file)


def test_road_design_export():
    """测试道路设计导出"""
    print("\n🔍 测试道路设计导出...")
    
    # 创建道路设计数据
    road_design_data = {
        'design_info': {
            'title': '测试道路设计',
            'designer': '测试工程师',
            'date': '2024-01-01',
            'scale': '1:1000'
        },
        'alignment': {
            'horizontal_elements': [
                {
                    'type': 'straight',
                    'start_point': [0, 0, 100],
                    'end_point': [100, 0, 100]
                },
                {
                    'type': 'circular',
                    'center': [100, 50, 100],
                    'radius': 50,
                    'start_angle': 270,
                    'end_angle': 360
                }
            ],
            'stations': [
                {'station': 0, 'point': [0, 0, 100]},
                {'station': 50, 'point': [50, 0, 100]},
                {'station': 100, 'point': [100, 0, 100]}
            ]
        },
        'cross_sections': [
            {
                'station': 0,
                'ground_profile': [[-5, 99], [0, 100], [5, 99]],
                'design_profile': [[-3.5, 100], [0, 100], [3.5, 100]]
            },
            {
                'station': 50,
                'ground_profile': [[-5, 100], [0, 101], [5, 100]],
                'design_profile': [[-3.5, 101], [0, 101], [3.5, 101]]
            }
        ]
    }
    
    with tempfile.NamedTemporaryFile(suffix='.dxf', delete=False) as f:
        output_file = f.name
    
    try:
        result = export_road_design_to_cad(road_design_data, output_file)
        
        if result.success:
            print(f"✅ 道路设计导出成功: {result.entity_count} 个实体")
            print(f"   图层数量: {result.layer_count}")
            print(f"   文件大小: {result.file_size} 字节")
        else:
            print(f"⚠️  道路设计导出失败: {result.message}")
        
    finally:
        if os.path.exists(output_file):
            os.unlink(output_file)


def test_terrain_conversion():
    """测试地形数据转换"""
    print("\n🔍 测试地形数据转换...")
    
    # 创建地形点数据
    terrain_points = []
    for i in range(5):
        for j in range(5):
            x = i * 10.0
            y = j * 10.0
            z = 100.0 + i + j * 0.5
            terrain_points.append(TerrainPoint(x, y, z))
    
    with tempfile.NamedTemporaryFile(suffix='.dxf', delete=False) as f:
        output_file = f.name
    
    try:
        result = convert_terrain_to_cad(terrain_points, output_file)
        
        if result.success:
            print(f"✅ 地形数据转换成功: {result.entity_count} 个点")
            print(f"   文件大小: {result.file_size} 字节")
            
            # 验证点数量
            assert result.entity_count == len(terrain_points), "转换的点数量不正确"
        else:
            print(f"⚠️  地形数据转换失败: {result.message}")
        
    finally:
        if os.path.exists(output_file):
            os.unlink(output_file)


def test_cad_converter():
    """测试CAD转换器"""
    print("\n🔍 测试CAD转换器...")
    
    converter = create_cad_converter()
    
    # 测试线条转换
    lines = [
        {
            'type': 'line',
            'start': [0, 0, 0],
            'end': [100, 100, 0]
        },
        {
            'type': 'polyline',
            'points': [[0, 0, 0], [50, 0, 0], [50, 50, 0], [0, 50, 0]],
            'closed': True
        }
    ]
    
    with tempfile.NamedTemporaryFile(suffix='.dxf', delete=False) as f:
        output_file = f.name
    
    try:
        result = converter.convert_lines_to_cad(lines, output_file)
        
        if result.success:
            print(f"✅ 线条转换成功: {result.entity_count} 个实体")
        else:
            print(f"⚠️  线条转换失败: {result.message}")
        
    finally:
        if os.path.exists(output_file):
            os.unlink(output_file)


def run_all_tests():
    """运行所有测试"""
    print("=" * 70)
    print("CAD数据处理服务测试")
    print("CAD Data Processing Service Tests")
    print("=" * 70)
    
    try:
        test_cad_importer()
        test_sample_dxf_creation()
        test_cad_entities()
        test_cad_drawing()
        test_cad_analyzer()
        test_cad_exporter()
        test_road_design_export()
        test_terrain_conversion()
        test_cad_converter()
        
        print("\n" + "=" * 70)
        print("🎉 所有CAD数据处理测试通过！")
        print("✅ CAD数据处理服务功能正常")
        print("=" * 70)
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        print("=" * 70)
        return False


if __name__ == "__main__":
    run_all_tests()