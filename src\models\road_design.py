"""
道路设计数据模型
Road Design Data Models
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field, validator

from src.models.base import BaseSchema, TimestampMixin, UUIDMixin, DesignStatus

try:
    from sqlalchemy import Column, String, Text, Float, Integer, JSON, ForeignKey
    from sqlalchemy.orm import relationship
    from src.models.base import SQLAlchemyBase
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False


# Pydantic模型
class Point2D(BaseModel):
    """二维点"""
    x: float
    y: float


class Point3D(BaseModel):
    """三维点"""
    x: float
    y: float
    z: float = 0.0


class BoundingBox(BaseModel):
    """边界框"""
    min_x: float
    min_y: float
    max_x: float
    max_y: float
    min_z: Optional[float] = None
    max_z: Optional[float] = None


class HorizontalElement(BaseModel):
    """水平线形元素"""
    element_type: str = Field(..., description="元素类型：直线、圆曲线、缓和曲线")
    start_station: float = Field(..., description="起始桩号")
    end_station: float = Field(..., description="结束桩号")
    length: float = Field(..., description="长度")
    start_point: Point2D = Field(..., description="起点坐标")
    end_point: Point2D = Field(..., description="终点坐标")
    radius: Optional[float] = Field(None, description="半径（圆曲线）")
    parameter_a: Optional[float] = Field(None, description="参数A（缓和曲线）")
    azimuth: Optional[float] = Field(None, description="方位角")
    
    @validator('element_type')
    def validate_element_type(cls, v):
        allowed_types = ['straight', 'circular', 'spiral']
        if v not in allowed_types:
            raise ValueError(f'元素类型必须是: {allowed_types}')
        return v


class VerticalElement(BaseModel):
    """竖直线形元素"""
    element_type: str = Field(..., description="元素类型：直坡段、竖曲线")
    start_station: float = Field(..., description="起始桩号")
    end_station: float = Field(..., description="结束桩号")
    length: float = Field(..., description="长度")
    start_elevation: float = Field(..., description="起点高程")
    end_elevation: float = Field(..., description="终点高程")
    grade: float = Field(..., description="坡度（%）")
    radius: Optional[float] = Field(None, description="竖曲线半径")
    
    @validator('element_type')
    def validate_element_type(cls, v):
        allowed_types = ['grade', 'vertical_curve']
        if v not in allowed_types:
            raise ValueError(f'元素类型必须是: {allowed_types}')
        return v


class StationingInfo(BaseModel):
    """桩号信息"""
    start_station: float = Field(..., description="起始桩号")
    end_station: float = Field(..., description="结束桩号")
    total_length: float = Field(..., description="总长度")
    station_interval: float = Field(default=20.0, description="桩号间距")


class RoadAlignment(BaseSchema):
    """道路中线"""
    horizontal_alignment: List[HorizontalElement] = Field(default_factory=list, description="水平线形")
    vertical_alignment: List[VerticalElement] = Field(default_factory=list, description="竖直线形")
    stationing: StationingInfo = Field(..., description="桩号信息")
    coordinate_system: str = Field(default="EPSG:4326", description="坐标系统")
    
    @validator('horizontal_alignment')
    def validate_horizontal_continuity(cls, v):
        """验证水平线形连续性"""
        if len(v) < 2:
            return v
        
        for i in range(1, len(v)):
            prev_end = v[i-1].end_station
            curr_start = v[i].start_station
            if abs(prev_end - curr_start) > 0.001:  # 允许1mm误差
                raise ValueError(f'水平线形不连续：桩号 {prev_end} 到 {curr_start}')
        return v


class CutFillAreas(BaseModel):
    """挖填面积"""
    cut_area: float = Field(default=0.0, description="挖方面积")
    fill_area: float = Field(default=0.0, description="填方面积")
    net_area: float = Field(default=0.0, description="净面积")


class CrossSection(BaseSchema):
    """横断面"""
    station: float = Field(..., description="桩号")
    ground_profile: List[Point2D] = Field(default_factory=list, description="地面线")
    design_profile: List[Point2D] = Field(default_factory=list, description="设计线")
    cut_fill_areas: CutFillAreas = Field(default_factory=CutFillAreas, description="挖填面积")
    road_width: float = Field(default=7.0, description="路面宽度")
    shoulder_width: float = Field(default=1.0, description="路肩宽度")
    slope_ratio: float = Field(default=1.5, description="边坡坡率")


class EarthworkVolume(BaseModel):
    """土方量"""
    total_cut_volume: float = Field(default=0.0, description="总挖方量")
    total_fill_volume: float = Field(default=0.0, description="总填方量")
    net_volume: float = Field(default=0.0, description="净土方量")
    station_volumes: List[Dict[str, float]] = Field(default_factory=list, description="分桩土方量")


class DesignStandards(BaseModel):
    """设计标准"""
    standard_name: str = Field(..., description="标准名称")
    version: str = Field(..., description="版本")
    design_speed: float = Field(..., description="设计速度 km/h")
    min_horizontal_radius: float = Field(..., description="最小水平曲线半径")
    max_grade: float = Field(..., description="最大纵坡")
    min_vertical_radius: float = Field(..., description="最小竖曲线半径")
    road_width: float = Field(..., description="标准路面宽度")
    shoulder_width: float = Field(..., description="标准路肩宽度")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="其他参数")


class RoadDesign(BaseSchema, TimestampMixin, UUIDMixin):
    """道路设计主模型"""
    name: str = Field(..., description="设计名称")
    description: Optional[str] = Field(None, description="设计描述")
    design_standards: DesignStandards = Field(..., description="设计标准")
    alignment: Optional[RoadAlignment] = Field(None, description="道路中线")
    cross_sections: List[CrossSection] = Field(default_factory=list, description="横断面")
    earthwork: Optional[EarthworkVolume] = Field(None, description="土方量")
    status: DesignStatus = Field(default=DesignStatus.DRAFT, description="设计状态")
    project_id: Optional[UUID] = Field(None, description="项目ID")
    designer: Optional[str] = Field(None, description="设计人员")
    reviewer: Optional[str] = Field(None, description="审核人员")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


# SQLAlchemy模型（如果可用）
if SQLALCHEMY_AVAILABLE:
    class RoadDesignDB(SQLAlchemyBase):
        """道路设计数据库模型"""
        __tablename__ = "road_designs"
        
        name = Column(String(255), nullable=False, index=True)
        description = Column(Text)
        design_standards = Column(JSON, nullable=False)
        alignment = Column(JSON)
        cross_sections = Column(JSON, default=list)
        earthwork = Column(JSON)
        status = Column(String(50), default=DesignStatus.DRAFT, index=True)
        project_id = Column(String(36), index=True)
        designer = Column(String(255))
        reviewer = Column(String(255))
        metadata = Column(JSON, default=dict)
        
        def to_pydantic(self) -> RoadDesign:
            """转换为Pydantic模型"""
            data = self.to_dict()
            
            # 转换JSON字段
            if data.get('design_standards'):
                data['design_standards'] = DesignStandards(**data['design_standards'])
            if data.get('alignment'):
                data['alignment'] = RoadAlignment(**data['alignment'])
            if data.get('cross_sections'):
                data['cross_sections'] = [CrossSection(**cs) for cs in data['cross_sections']]
            if data.get('earthwork'):
                data['earthwork'] = EarthworkVolume(**data['earthwork'])
                
            return RoadDesign(**data)
        
        @classmethod
        def from_pydantic(cls, road_design: RoadDesign):
            """从Pydantic模型创建"""
            data = road_design.dict()
            
            # 转换复杂对象为JSON
            if data.get('design_standards'):
                data['design_standards'] = data['design_standards'].dict()
            if data.get('alignment'):
                data['alignment'] = data['alignment'].dict()
            if data.get('cross_sections'):
                data['cross_sections'] = [cs.dict() for cs in data['cross_sections']]
            if data.get('earthwork'):
                data['earthwork'] = data['earthwork'].dict()
                
            return cls(**data)
else:
    RoadDesignDB = None


# 工厂函数
def create_default_design_standards() -> DesignStandards:
    """创建默认设计标准"""
    return DesignStandards(
        standard_name="露天矿山道路设计标准",
        version="v1.0",
        design_speed=40.0,
        min_horizontal_radius=60.0,
        max_grade=8.0,
        min_vertical_radius=400.0,
        road_width=7.0,
        shoulder_width=1.0,
        parameters={
            "min_sight_distance": 40.0,
            "max_superelevation": 6.0,
            "min_curve_length": 30.0
        }
    )


def create_sample_road_design() -> RoadDesign:
    """创建示例道路设计"""
    return RoadDesign(
        name="示例道路设计",
        description="用于测试的示例道路设计",
        design_standards=create_default_design_standards(),
        designer="系统",
        metadata={"created_by": "system", "purpose": "testing"}
    )