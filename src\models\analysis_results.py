"""
分析结果数据模型
Analysis Results Data Models
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from uuid import UUID
from enum import Enum
from pydantic import BaseModel, Field, validator

from src.models.base import BaseSchema, TimestampMixin, UUIDMixin

try:
    from sqlalchemy import Column, String, Text, Float, Integer, JSON, Boolean
    from src.models.base import SQLAlchemyBase
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False


# 枚举类型
class ConflictSeverity(str, Enum):
    """冲突严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ConflictType(str, Enum):
    """冲突类型"""
    GEOMETRIC = "geometric"
    SAFETY = "safety"
    STANDARD = "standard"
    ENVIRONMENTAL = "environmental"
    OPERATIONAL = "operational"


class OptimizationObjective(str, Enum):
    """优化目标"""
    DISTANCE = "distance"
    COST = "cost"
    TIME = "time"
    FUEL = "fuel"
    SAFETY = "safety"
    ENVIRONMENTAL = "environmental"


class AnalysisStatus(str, Enum):
    """分析状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


# 冲突检测相关模型
class ConflictLocation(BaseModel):
    """冲突位置"""
    station: float = Field(..., description="桩号")
    x: float = Field(..., description="X坐标")
    y: float = Field(..., description="Y坐标")
    z: Optional[float] = Field(None, description="Z坐标")
    description: str = Field(..., description="位置描述")


class ConflictItem(BaseSchema, UUIDMixin):
    """冲突项"""
    conflict_type: ConflictType = Field(..., description="冲突类型")
    severity: ConflictSeverity = Field(..., description="严重程度")
    title: str = Field(..., description="冲突标题")
    description: str = Field(..., description="详细描述")
    location: ConflictLocation = Field(..., description="冲突位置")
    affected_elements: List[str] = Field(default_factory=list, description="受影响的元素")
    recommendations: List[str] = Field(default_factory=list, description="建议措施")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="相关参数")
    is_resolved: bool = Field(default=False, description="是否已解决")
    resolution_notes: Optional[str] = Field(None, description="解决方案说明")


class SeveritySummary(BaseModel):
    """严重程度汇总"""
    critical_count: int = Field(default=0, description="严重冲突数量")
    high_count: int = Field(default=0, description="高级冲突数量")
    medium_count: int = Field(default=0, description="中级冲突数量")
    low_count: int = Field(default=0, description="低级冲突数量")
    total_count: int = Field(default=0, description="总冲突数量")
    
    def calculate_total(self):
        """计算总数"""
        self.total_count = (self.critical_count + self.high_count + 
                           self.medium_count + self.low_count)


class ConflictReport(BaseSchema, TimestampMixin, UUIDMixin):
    """冲突检测报告"""
    design_id: UUID = Field(..., description="设计ID")
    design_name: str = Field(..., description="设计名称")
    conflicts: List[ConflictItem] = Field(default_factory=list, description="冲突列表")
    severity_summary: SeveritySummary = Field(default_factory=SeveritySummary, description="严重程度汇总")
    recommendations: List[str] = Field(default_factory=list, description="总体建议")
    analysis_parameters: Dict[str, Any] = Field(default_factory=dict, description="分析参数")
    execution_time: float = Field(..., description="执行时间（秒）")
    status: AnalysisStatus = Field(default=AnalysisStatus.COMPLETED, description="分析状态")
    
    def add_conflict(self, conflict: ConflictItem):
        """添加冲突"""
        self.conflicts.append(conflict)
        self.update_severity_summary()
    
    def update_severity_summary(self):
        """更新严重程度汇总"""
        summary = SeveritySummary()
        for conflict in self.conflicts:
            if conflict.severity == ConflictSeverity.CRITICAL:
                summary.critical_count += 1
            elif conflict.severity == ConflictSeverity.HIGH:
                summary.high_count += 1
            elif conflict.severity == ConflictSeverity.MEDIUM:
                summary.medium_count += 1
            elif conflict.severity == ConflictSeverity.LOW:
                summary.low_count += 1
        
        summary.calculate_total()
        self.severity_summary = summary


# 安全分析相关模型
class SafetyParameter(BaseModel):
    """安全参数"""
    parameter_name: str = Field(..., description="参数名称")
    current_value: float = Field(..., description="当前值")
    standard_value: float = Field(..., description="标准值")
    unit: str = Field(..., description="单位")
    is_compliant: bool = Field(..., description="是否符合标准")
    deviation: float = Field(..., description="偏差值")
    
    @validator('deviation', always=True)
    def calculate_deviation(cls, v, values):
        """计算偏差"""
        current = values.get('current_value', 0)
        standard = values.get('standard_value', 0)
        return current - standard


class SafetyAnalysisSection(BaseModel):
    """安全分析段"""
    start_station: float = Field(..., description="起始桩号")
    end_station: float = Field(..., description="结束桩号")
    parameters: List[SafetyParameter] = Field(default_factory=list, description="安全参数")
    overall_compliance: bool = Field(default=True, description="整体符合性")
    issues: List[str] = Field(default_factory=list, description="问题列表")


class SafetyReport(BaseSchema, TimestampMixin, UUIDMixin):
    """安全分析报告"""
    design_id: UUID = Field(..., description="设计ID")
    design_name: str = Field(..., description="设计名称")
    analysis_sections: List[SafetyAnalysisSection] = Field(default_factory=list, description="分析段")
    overall_safety_score: float = Field(..., description="总体安全评分")
    compliance_rate: float = Field(..., description="符合率")
    critical_issues: List[str] = Field(default_factory=list, description="关键问题")
    recommendations: List[str] = Field(default_factory=list, description="安全建议")
    execution_time: float = Field(..., description="执行时间（秒）")
    status: AnalysisStatus = Field(default=AnalysisStatus.COMPLETED, description="分析状态")


# 路线优化相关模型
class RoutePoint(BaseModel):
    """路线点"""
    x: float = Field(..., description="X坐标")
    y: float = Field(..., description="Y坐标")
    z: Optional[float] = Field(None, description="Z坐标")
    station: Optional[float] = Field(None, description="桩号")
    properties: Dict[str, Any] = Field(default_factory=dict, description="属性")


class Route(BaseModel):
    """路线"""
    name: str = Field(..., description="路线名称")
    points: List[RoutePoint] = Field(..., description="路线点")
    total_length: float = Field(..., description="总长度")
    total_cost: float = Field(..., description="总成本")
    travel_time: float = Field(..., description="行驶时间")
    fuel_consumption: float = Field(..., description="燃料消耗")
    safety_score: float = Field(..., description="安全评分")
    environmental_impact: float = Field(..., description="环境影响")
    properties: Dict[str, Any] = Field(default_factory=dict, description="其他属性")


class OptimizationConstraints(BaseModel):
    """优化约束条件"""
    max_grade: Optional[float] = Field(None, description="最大坡度")
    min_radius: Optional[float] = Field(None, description="最小半径")
    max_length: Optional[float] = Field(None, description="最大长度")
    avoid_areas: List[Dict[str, Any]] = Field(default_factory=list, description="避让区域")
    weight_factors: Dict[str, float] = Field(default_factory=dict, description="权重因子")
    custom_constraints: Dict[str, Any] = Field(default_factory=dict, description="自定义约束")


class OptimalRoute(BaseModel):
    """最优路线"""
    route: Route = Field(..., description="路线信息")
    objective_values: Dict[OptimizationObjective, float] = Field(..., description="目标值")
    rank: int = Field(..., description="排名")
    pareto_optimal: bool = Field(default=False, description="是否为帕累托最优")
    trade_offs: Dict[str, str] = Field(default_factory=dict, description="权衡说明")


class CostComparison(BaseModel):
    """成本对比"""
    original_cost: float = Field(..., description="原始成本")
    optimized_cost: float = Field(..., description="优化后成本")
    cost_savings: float = Field(..., description="成本节省")
    savings_percentage: float = Field(..., description="节省百分比")
    cost_breakdown: Dict[str, float] = Field(default_factory=dict, description="成本分解")


class PerformanceMetrics(BaseModel):
    """性能指标"""
    optimization_time: float = Field(..., description="优化时间")
    iterations: int = Field(..., description="迭代次数")
    convergence_achieved: bool = Field(..., description="是否收敛")
    solution_quality: float = Field(..., description="解质量")
    algorithm_used: str = Field(..., description="使用的算法")


class OptimizationResult(BaseSchema, TimestampMixin, UUIDMixin):
    """优化结果"""
    design_id: UUID = Field(..., description="原始设计ID")
    design_name: str = Field(..., description="设计名称")
    optimization_objectives: List[OptimizationObjective] = Field(..., description="优化目标")
    constraints: OptimizationConstraints = Field(..., description="约束条件")
    original_route: Route = Field(..., description="原始路线")
    optimized_routes: List[OptimalRoute] = Field(..., description="优化路线")
    best_route: OptimalRoute = Field(..., description="最佳路线")
    cost_comparison: CostComparison = Field(..., description="成本对比")
    performance_metrics: PerformanceMetrics = Field(..., description="性能指标")
    recommendations: List[str] = Field(default_factory=list, description="优化建议")
    status: AnalysisStatus = Field(default=AnalysisStatus.COMPLETED, description="优化状态")


# SQLAlchemy模型（如果可用）
if SQLALCHEMY_AVAILABLE:
    class ConflictReportDB(SQLAlchemyBase):
        """冲突报告数据库表"""
        __tablename__ = "conflict_reports"
        
        design_id = Column(String(36), nullable=False, index=True)
        design_name = Column(String(255), nullable=False)
        conflicts = Column(JSON, default=list)
        severity_summary = Column(JSON, nullable=False)
        recommendations = Column(JSON, default=list)
        analysis_parameters = Column(JSON, default=dict)
        execution_time = Column(Float, nullable=False)
        status = Column(String(50), default=AnalysisStatus.COMPLETED)
        
        def to_pydantic(self) -> ConflictReport:
            """转换为Pydantic模型"""
            data = self.to_dict()
            if data.get('conflicts'):
                data['conflicts'] = [ConflictItem(**c) for c in data['conflicts']]
            if data.get('severity_summary'):
                data['severity_summary'] = SeveritySummary(**data['severity_summary'])
            return ConflictReport(**data)
    
    class SafetyReportDB(SQLAlchemyBase):
        """安全报告数据库表"""
        __tablename__ = "safety_reports"
        
        design_id = Column(String(36), nullable=False, index=True)
        design_name = Column(String(255), nullable=False)
        analysis_sections = Column(JSON, default=list)
        overall_safety_score = Column(Float, nullable=False)
        compliance_rate = Column(Float, nullable=False)
        critical_issues = Column(JSON, default=list)
        recommendations = Column(JSON, default=list)
        execution_time = Column(Float, nullable=False)
        status = Column(String(50), default=AnalysisStatus.COMPLETED)
        
        def to_pydantic(self) -> SafetyReport:
            """转换为Pydantic模型"""
            data = self.to_dict()
            if data.get('analysis_sections'):
                data['analysis_sections'] = [SafetyAnalysisSection(**s) for s in data['analysis_sections']]
            return SafetyReport(**data)
    
    class OptimizationResultDB(SQLAlchemyBase):
        """优化结果数据库表"""
        __tablename__ = "optimization_results"
        
        design_id = Column(String(36), nullable=False, index=True)
        design_name = Column(String(255), nullable=False)
        optimization_objectives = Column(JSON, nullable=False)
        constraints = Column(JSON, nullable=False)
        original_route = Column(JSON, nullable=False)
        optimized_routes = Column(JSON, default=list)
        best_route = Column(JSON, nullable=False)
        cost_comparison = Column(JSON, nullable=False)
        performance_metrics = Column(JSON, nullable=False)
        recommendations = Column(JSON, default=list)
        status = Column(String(50), default=AnalysisStatus.COMPLETED)
        
        def to_pydantic(self) -> OptimizationResult:
            """转换为Pydantic模型"""
            data = self.to_dict()
            # 转换复杂对象
            if data.get('constraints'):
                data['constraints'] = OptimizationConstraints(**data['constraints'])
            if data.get('original_route'):
                data['original_route'] = Route(**data['original_route'])
            if data.get('optimized_routes'):
                data['optimized_routes'] = [OptimalRoute(**r) for r in data['optimized_routes']]
            if data.get('best_route'):
                data['best_route'] = OptimalRoute(**data['best_route'])
            if data.get('cost_comparison'):
                data['cost_comparison'] = CostComparison(**data['cost_comparison'])
            if data.get('performance_metrics'):
                data['performance_metrics'] = PerformanceMetrics(**data['performance_metrics'])
            return OptimizationResult(**data)

else:
    ConflictReportDB = None
    SafetyReportDB = None
    OptimizationResultDB = None


# 工厂函数
def create_sample_conflict_report(design_id: UUID, design_name: str) -> ConflictReport:
    """创建示例冲突报告"""
    # 创建示例冲突
    conflict1 = ConflictItem(
        conflict_type=ConflictType.GEOMETRIC,
        severity=ConflictSeverity.HIGH,
        title="曲线半径不足",
        description="K0+150处水平曲线半径为45m，小于标准要求的60m",
        location=ConflictLocation(
            station=150.0,
            x=1000.0,
            y=2000.0,
            description="K0+150水平曲线"
        ),
        affected_elements=["horizontal_alignment"],
        recommendations=["增大曲线半径至60m以上", "降低设计速度"]
    )
    
    conflict2 = ConflictItem(
        conflict_type=ConflictType.SAFETY,
        severity=ConflictSeverity.MEDIUM,
        title="视距不足",
        description="K0+300处停车视距为35m，小于标准要求的40m",
        location=ConflictLocation(
            station=300.0,
            x=1200.0,
            y=2100.0,
            description="K0+300竖曲线"
        ),
        affected_elements=["vertical_alignment"],
        recommendations=["增大竖曲线半径", "调整纵坡设计"]
    )
    
    report = ConflictReport(
        design_id=design_id,
        design_name=design_name,
        conflicts=[conflict1, conflict2],
        recommendations=["整体优化线形设计", "加强安全设施配置"],
        execution_time=2.5
    )
    
    report.update_severity_summary()
    return report


def create_sample_optimization_result(design_id: UUID, design_name: str) -> OptimizationResult:
    """创建示例优化结果"""
    # 原始路线
    original_route = Route(
        name="原始路线",
        points=[
            RoutePoint(x=0, y=0, z=100),
            RoutePoint(x=500, y=200, z=120),
            RoutePoint(x=1000, y=500, z=140)
        ],
        total_length=1200.0,
        total_cost=1000000.0,
        travel_time=180.0,
        fuel_consumption=50.0,
        safety_score=7.5,
        environmental_impact=3.2
    )
    
    # 优化路线
    optimized_route = Route(
        name="优化路线",
        points=[
            RoutePoint(x=0, y=0, z=100),
            RoutePoint(x=400, y=150, z=115),
            RoutePoint(x=800, y=350, z=130),
            RoutePoint(x=1000, y=500, z=140)
        ],
        total_length=1150.0,
        total_cost=950000.0,
        travel_time=165.0,
        fuel_consumption=45.0,
        safety_score=8.2,
        environmental_impact=2.8
    )
    
    best_route = OptimalRoute(
        route=optimized_route,
        objective_values={
            OptimizationObjective.DISTANCE: 1150.0,
            OptimizationObjective.COST: 950000.0,
            OptimizationObjective.TIME: 165.0
        },
        rank=1,
        pareto_optimal=True
    )
    
    cost_comparison = CostComparison(
        original_cost=1000000.0,
        optimized_cost=950000.0,
        cost_savings=50000.0,
        savings_percentage=5.0
    )
    
    performance_metrics = PerformanceMetrics(
        optimization_time=45.2,
        iterations=150,
        convergence_achieved=True,
        solution_quality=0.95,
        algorithm_used="Multi-objective Genetic Algorithm"
    )
    
    return OptimizationResult(
        design_id=design_id,
        design_name=design_name,
        optimization_objectives=[OptimizationObjective.DISTANCE, OptimizationObjective.COST],
        constraints=OptimizationConstraints(max_grade=8.0, min_radius=60.0),
        original_route=original_route,
        optimized_routes=[best_route],
        best_route=best_route,
        cost_comparison=cost_comparison,
        performance_metrics=performance_metrics,
        recommendations=["采用优化路线可节省5%成本", "建议进一步优化环境影响"]
    )